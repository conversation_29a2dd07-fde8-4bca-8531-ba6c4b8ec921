<template>
  <el-drawer
    v-model="visibleDrawer"
    :title="title"
    :close-on-click-modal="false"
    size="800px"
    @close="close"
  >
    <div class="receiveTransportDialog" v-loading="loading">
      <div class="receive-transport-content">
        <div class="purchase-info">
          <el-descriptions :column="3" style="width: 100%">
            <el-descriptions-item
              :label="$t('shippingReceipt.label.purchaseCode')"
            >
              {{ purchaseOrderInfo.purchaseCode }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.purchaseType')"
            >
              {{ purchaseOrderInfo.purchaseOrderType }}
            </el-descriptions-item>

            <el-descriptions-item
              v-if="purchaseOrderInfo.purchaseOrderType !== '市场自采'"
              :label="$t('shippingReceipt.label.supplier')"
            >
              {{ purchaseOrderInfo.supplierName }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('shippingReceipt.label.source')">
              {{ purchaseOrderInfo.purchaseOrderSource }}
            </el-descriptions-item>

            <el-descriptions-item
              :label="$t('shippingReceipt.label.plannedDeliveryDate')"
            >
              {{ parseDateTime(purchaseOrderInfo.planDeliveryDate, "date") }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.buyerName')"
            >
              {{ purchaseOrderInfo.buyerName }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.createTime')"
            >
              {{ parseDateTime(purchaseOrderInfo.createTime, "dateTime") }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('shippingReceipt.label.creator')">
              {{ purchaseOrderInfo.createUserName }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('shippingReceipt.label.remark')">
              {{ purchaseOrderInfo?.remark  ?  purchaseOrderInfo.remark : '-'}}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="header-line"></div>
        <div class="product-content-table">
          <el-descriptions :column="2" style="width: 100%">
            <el-descriptions-item
              :label="$t('shippingReceipt.label.createUserName')"
            >
              {{ purchaseOrderInfo.createUserName }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.receiveTransportDate')"
            >
              {{ parseDateTime(purchaseOrderInfo?.receiveTransportDate, "dateTime") }}
            </el-descriptions-item>
          </el-descriptions>
          <el-form
            :model="form"
            ref="fromRef"
            label-width="120px"
            label-position="right"
          >
            <el-table
              highlight-current-row
              stripe
              :data="form.tableData || []">
              <template #empty>
                <Empty/>
              </template>
              <el-table-column :label="$t('shippingReceipt.label.productName')" prop="productName" />
              <el-table-column :label="$t('shippingReceipt.label.productUnit')" prop="unitName" min-width="90" />
              <el-table-column :label="$t('shippingReceipt.label.price')" prop="unitPriceReceivedThisTime" min-width="120">
                <template #default="scope">
                  <template v-if="drawerType == 'detail'">
                    <span v-if="scope.row?.unitPriceReceivedThisTime || scope.row?.unitPriceReceivedThisTime == 0">{{ scope.row.confirmStatus === "USD" ? "$" : "￥" }}{{ scope.row.unitPriceReceivedThisTime }}</span>
                  </template>
                  <template v-if="drawerType == 'confirm'">
                    <el-form-item class="mt15px" label-width="0px"
                                  :prop="'tableData.'+scope.$index+'.unitPriceReceivedThisTime'"
                                  :rules="[
                                     {required:true,message:'请输入单价',trigger:['blur','change']},
                                     {pattern: unitPriceRegExp,message:t('shippingReceipt.rules.unitPriceFormat'), trigger: ['blur','change']}
                                   ]">
                      <el-input
                        v-model="scope.row.unitPriceReceivedThisTime"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                      ></el-input>
                    </el-form-item>
                  </template>

                </template>
              </el-table-column>
              <el-table-column :label="$t('shippingReceipt.label.receiverCount')" prop="receivedThisTime" min-width="120" />
              <el-table-column :label="$t('shippingReceipt.label.receiverCoast')" prop="receivedAmountThisTime" min-width="60">
                <template #default="scope">
                  <!--                confirmStatus 与后端沟通没有此字段，默认展示rmb，与产品已同步-->
                  <span v-if="scope.row?.receivedAmountThisTime || scope.row?.receivedAmountThisTime == 0">
                  {{ scope.row.confirmStatus === "USD" ? "$" : "￥"
                    }}{{ scope.row.receivedAmountThisTime }}
                </span>
                </template>
              </el-table-column>
              
            </el-table>
          </el-form>

         <div class="inspection-table-title">
           {{ t('shippingReceipt.label.inspectionTableTitle') }}
         </div>
          <el-table :data="inspectionTableData || []" border :span-method="spanMethodHandler">
            <template #empty>
              <Empty/>
            </template>
            <el-table-column :label="$t('shippingReceipt.label.productName')" prop="productName" show-overflow-tooltip min-width="120"/>
            <el-table-column :label="$t('shippingReceipt.label.specification')" prop="specification" min-width="90" />
            <el-table-column :label="$t('shippingReceipt.label.quantity')" prop="quantity" min-width="90" />
            <el-table-column :label="$t('shippingReceipt.label.proportion')" prop="proportion" min-width="90" >
              <template #default="scope">
                <span v-if="scope.row?.proportion || scope.row?.proportion == 0">{{scope.row?.proportion}}%</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('shippingReceipt.label.deductionAmount')" prop="deductionAmount" min-width="90" />
            <el-table-column :label="$t('shippingReceipt.label.deductionDesc')" prop="deductionDesc" show-overflow-tooltip min-width="120" />
            <el-table-column :label="$t('shippingReceipt.label.remark')" prop="remark"/>
            <el-table-column :label="$t('shippingReceipt.label.attachment')" prop="imagesUrls" min-width="90" >
              <template #default="scope">
                <span v-if="scope.row?.imagesUrls?.length">
                  <el-button type="primary" link @click="previewAttachment(scope.row, scope.$index)">
                    {{ t('shippingReceipt.button.viewBtn') }}
                  </el-button>
                </span>
              </template>
            </el-table-column>>
          </el-table>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div class="end">
          <div class="end-left normal14-font">
            <div>{{ $t("shippingReceipt.label.total") }}:</div>
            <div class="ml-20px">
              {{ $t("shippingReceipt.label.receivedCount") }}:
              <span class="primary14-font">
                {{ purchaseOrderInfo.totalReceivedCount }}
              </span>
            </div>
            <div class="ml-20px">
              {{ $t("shippingReceipt.label.includedTax") }}:
              <span class="primary14-font">
                {{ purchaseOrderInfo.amountCurrency === "USD" ? "$" : "￥"
                }}{{ purchaseOrderInfo.totalReceivedAmount }}
              </span>
            </div>
          </div>
          <div class="end-right">
            <el-button @click="close()">
              {{ $t("common.cancel") }}
            </el-button>

            <el-button
              type="primary"
              v-if="drawerType === 'confirm'"
              @click="confirmAction()"
              v-hasPerm="['pms:CT:shippingReceipt:confirm']"
            >
              {{ $t("shippingReceipt.button.confirmTitle") }}
            </el-button>
          </div>
        </div>
      </div>
    </template>

    <!--上传-->
    <UploadDialog v-model:visible="uploadDialog.visible" ref="uploadDialogRef" :ifDrag=false
                  :showUploadBtn=false />
  </el-drawer>
</template>
<script setup lang="ts">

import ShippingReceiptAPI, {inspectionData, SRPurchaseOrderData,} from "@/modules/pms/api/shippingReceipt";
import { parseDateTime } from "@/core/utils/index.js";
import UploadDialog
  from "@/modules/wms/views/insideWarehouseManagement/qualityInspectionOrder/components/uploadDialog.vue";

const emit = defineEmits(["onSubmit","cancel"]);
const { t } = useI18n();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  drawerType: {
    type: String,
    default: "detail",
  },
  data: {
    type: Object,
    default: () => {},
  },
});
const receiveTransportId = ref(props?.data?.receiveTransportId);
const unitPriceRegExp = /^(-?0(?:\.\d{1,4})?|[1-9]\d{0,6}(?:\.\d{1,4})?)$/;
const form = reactive({
   tableData: []
})

const visibleDrawer = ref(false);
watch(()=> props.visible, (val) => {
  visibleDrawer.value = val;
  },{immediate: true, deep: true})

const loading = ref(false);



/*质检单*/
function queryInspectionByReceiveTransportCode() {
  loading.value = true;
  ShippingReceiptAPI.queryInspectionByReceiveTransportCode({
    receiveTransportId: receiveTransportId.value
  }).then((data) => {
    groupByAllData(data?.productList)
  }).finally(() => {
    loading.value = false;
  });
}

const inspectionTableData = ref([]);
function groupByAllData(data = []) {
  if(data?.length){
    data?.forEach(productItem => {
      if(productItem?.qualityInspectionRecordsProductDetailVOList?.length){
        productItem?.qualityInspectionRecordsProductDetailVOList?.forEach(qualityInspectionItem => {
          let item = {
            ...qualityInspectionItem,
            productName: productItem.productName,
            deductionAttachment: productItem.deductionAttachment,
            deductionDesc: productItem.deductionDesc,
            deductionAmount: productItem.deductionAmount,
            imagesUrls: productItem?.imagesUrls ? JSON.parse(productItem?.imagesUrls) : []
          }
          inspectionTableData.value.push(item)
        })
      }
    })
  }
}


/** 确认收运单 */
function confirmAction() {
  let receiveTransportConfirmListRequestDTOS = ref([]);
  form?.tableData?.forEach((item) => {
    let data = {
      amountCurrency: item?.amountCurrency,
      receiveTransportDetailId: item.receiveTransportDetailId,
      unitPriceReceivedThisTime: item.unitPriceReceivedThisTime,
    };
    receiveTransportConfirmListRequestDTOS.value.push(data);
  });
  let params = {
    receiveTransportConfirmListRequestDTOS: receiveTransportConfirmListRequestDTOS.value,
    receiveTransportId: receiveTransportId.value,
  };
  ShippingReceiptAPI.confirmReceiveTransport(params)
    .then(() => {
      ElMessage.success(t('shippingReceipt.message.confirmSuccess'))
      emit("onSubmit");
      close();
    })
    .finally(() => (loading.value = false));
}

/*合并入库质检单table单元格*/
function spanMethodHandler(params: any) {
  const { row, column, rowIndex, columnIndex } = params;
  if ([0,4,5,6].includes(columnIndex)) {
    // 如果当前行是第一行，或者当前行的值与上一行不同，则不合并
    if (rowIndex === 0 || row?.productName !== inspectionTableData.value[rowIndex - 1]?.productName) {
      let count = 1;
      // 统计后续有多少个相同的值
      for (let i = rowIndex + 1; i < inspectionTableData.value?.length; i++) {
        if (inspectionTableData.value[i]?.productName === row?.productName) {
          count++;
        } else {
          break;
        }
      }
      return {
        rowspan: count,
        colspan: 1
      };
    } else {
      // 否则不显示该单元格
      return {
        rowspan: 0,
        colspan: 0
      };
    }
  }
  // 其他列不合并
  return {
    rowspan: 1,
    colspan: 1
  };
}

function close() {
  emit("cancel");
  visibleDrawer.value = false;
}

const uploadDialog = reactive({
  visible: false,
});

// 上传
const uploadDialogRef = ref()
function previewAttachment(row: any, index: any) {
  uploadDialog.visible = true;
  uploadDialogRef.value?.setEditType("add");
  if (row?.imagesUrls?.length) {
    uploadDialogRef.value?.setFormData(row.imagesUrls);
  }
}

const purchaseOrderInfo = ref<SRPurchaseOrderData>({});
function getReceiveTransportDetails() {
  let params = {
    receiveTransportId: receiveTransportId.value,
  };
  loading.value = true;
  ShippingReceiptAPI.receiveTransportDetails(params)
    .then((data) => {
      purchaseOrderInfo.value = data || {}
      form.tableData = data?.receiveTransportDetailVOList || []
    })
    .finally(() => {
      loading.value = false;
    });
}
onMounted(() => {
    getReceiveTransportDetails()
    //查询质检单列表
    queryInspectionByReceiveTransportCode()
})

</script>

<style scoped lang="scss">
.receiveTransportDialog {
  padding: 5px;

  .receive-transport-content {
    margin-top: 20px;
  }
}

.end {
  display: flex;
  justify-content: space-between;

  .end-left {
    display: flex;

    div {
      align-content: center;
    }

    span {
      color: #c00c1d;
    }
  }
}
.header-line{
  border-bottom: 1px solid #ebeef5;
  margin: 22px 0;
}
.inspection-table-title {
  text-align: center;
  color: #323233;
  margin: 22px 0;
}
</style>
<style scoped>
::v-deep .el-input-group__append {
  padding: 10px;
}

::v-deep .custom-form-item {
  margin-bottom: 15px;
}
</style>
