@use "./reset";

.app-container {
  //padding: 15px;
  //padding: 20px;
  height: 100%;
}

.search-container {
  //padding: 18px 0 0 10px;
  //margin-bottom: 10px;
  padding: 20px 20px 2px 20px;
  margin-bottom: 10px;
  background-color: var(--el-bg-color-overlay);
  //border: 1px solid var(--el-border-color-light);
  border-radius: 2px;
  //box-shadow: var(--el-box-shadow-light);
}
.el-card{
  border-radius: 2px !important;
  border: none !important;
}

.table-container > .el-card__header {
  //padding: calc(var(--el-card-padding) - 8px) var(--el-card-padding);
  padding: 20px 20px 0px 20px;
  border-bottom: 0px;
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32 160 255);
  }
}

/*.search-container .el-form .el-input {
  width: 16rem !important;
}*/
.display-none {
  display: none;
}

.el-drawer__body{
  border-top: 1px solid #E5E7F3;
  border-bottom: 1px solid #E5E7F3;
  padding: 24px !important;
}
.el-drawer__footer {
  padding: 12px 16px !important;
}
.el-drawer__header {
  padding: 16px 24px !important;
  margin-bottom: 0px !important;
  .el-drawer__close-btn {
    font-size: 18px;
  }
}
.el-drawer__title{
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #323233;
  line-height: 24px;
  text-align: left;
  font-style: normal;
}

.el-message-box-icon--warning {
  color: var(--el-color-primary) !important;
}

.el-message-box {
  padding: 0px !important;
  border-radius: 2px !important;
}
.el-message-box__header{
  padding: 20px 30px;
  border-bottom: 1px solid #E5E7F3;
  .el-message-box__title{
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
  .el-message-box__close {
    font-size: 18px !important;
  }
}
.el-message-box__btns {
  padding: 20px 30px;
  border-top: 1px solid #E5E7F3;
}
.el-message-box__content {
  padding: 76px 30px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #52585F;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}
.el-message-box__headerbtn {
  height: 64px !important;
}

.pagination {
  padding: 16px 0px 0px 0px !important;
}

.el-pagination {
  display: flex;
  justify-content: center;
}
.flex-center-but{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-center-space-start{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.flex-center-start{
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
}

.el-cascader{
  width: 100%;
}
.el-button--primary.is-plain {
  background: #ffffff !important;
}
.el-button--primary.is-plain:hover{
  background: #ffffff !important;
  color: var(--el-color-primary) !important;
}
// 卡片标题
.card-title{
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #52585F;
  text-align: left;

  font-style: normal;
  display: flex;
  align-items: center;
  &::before{
    content: '';
    display: inline-block;

    width: 4px;
    height: 16px;
    background: #762ADB ;
    border-radius: 2px;

    margin-right: 8px;
  }
}
// 信息展示label
.card-form-label{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #90979E;
  line-height: 32px;
  text-align: center;
  font-style: normal;
  margin-right: 8px;
}
// 信息展示内容
.card-form-text{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #151719;
  line-height: 32px;
  text-align: left;
  font-style: normal;
}
// 卡片底部边框
.card-bottomm-border{
  border-bottom: 1px solid #E5E7F3;
}
// 主色调文字
.primary-text{
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #762ADB ;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}
// 表单标签
.form-label{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #90979E;
  line-height: 32px;
  text-align: center;
  font-style: normal;
  word-break: keep-all;
  display: inline-block;
}
// 表单内容
.form-text{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #151719;
  line-height: 32px;
  text-align: left;
  font-style: normal;
}
// 表格中的操作文字
.operation-text{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #762ADB ;
  line-height: 20px;
  text-align: center;
  font-style: normal;
}

// 详情页面标题样式
.page-title{
  padding: 13px 21px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 1px solid #E5E7F3;
  .purchase-title{
    display: flex;
    align-items: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    margin-right: 10px;
  }
}
.line{
  border-bottom: 1px solid #E5E7F3;
}
// 状态tab样式
.purchase{
  width: 80px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  font-style: normal;
  text-align: center;
  line-height: 16px;
  .purchase-status{
    display: block;
    padding: 4px 8px;
    border-radius: 2px;
  }
  .purchase-status-color1{
    background: rgba(255,156,0,0.08);
    border: 1px solid rgba(255,156,0,0.2);
    color: #FF9C00 ;
  }
  .purchase-status-color2{
    background: rgba(64,158,255,0.08);
    border: 1px solid rgba(64,158,255,0.2);
    color: #409EFF;
  }
  .purchase-status-color3{
    background: rgba(41,182,16,0.08);
    border: 1px solid rgba(41,182,16,0.2);
    color:#29B610;
  }
  .purchase-status-color4{
    background: rgba(97, 59, 236, 0.08);
    border: 1px solid rgba(97, 59, 236,0.2);
    color:#613BEC;
  }
  .purchase-status-color5{
    background: rgba(255, 77, 79, 0.08);
    border: 1px solid rgba(255, 77, 79, 0.20);
    color:#FF4D4F;
  }
  .purchase-status-color0{
    background: rgba(144,151,158,0.1);
    border: 1px solid rgba(200,201,204,0.2);
    color: #90979E;
  }
}
.page-content{
  padding:0px 20px 9px 20px;
}
// 详情页面小标题样式
.title-lable{
  padding: 20px 0px 15px 0px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .title-line{
    width: 4px;
    height: 16px;
    background: var(--el-color-primary);
    border-radius: 2px;
    margin-right: 12px;
  }
  .title-content {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #52585F;
    font-style: normal;
  }
}
// 详情页面按钮位置
.page-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 9px 20px;
  border-top: 1px solid #E5E7F3;
}

// 默认表单样式
.el-form-item--default .el-form-item__label {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #151719 !important;
  font-style: normal !important;
  padding: 0 8px 0 0;
}
.el-form-item--default .el-form-item__content {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #151719 !important;
  font-style: normal !important;
}
.el-input__inner {
  font-family: PingFangSC, PingFang SC !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #52585F !important;
  font-style: normal !important;
}

// 详情页面表单样式
.grad-row{
  .el-form-item__label {
    font-family: PingFangSC, PingFang SC !important;
    font-weight: 400 !important;
    font-size: 14px !important;
    color: #90979E !important;
    font-style: normal !important;
  }
  .el-form-item__content {
    font-family: PingFangSC, PingFang SC !important;
    font-weight: 400 !important;
    font-size: 14px !important;
    color: #151719 !important;
    font-style: normal !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    word-break: break-all !important;
    span {
      overflow-wrap: break-word !important;
      white-space: normal !important;
      word-break: break-all !important;
    }
  }
}

// 商品组合样式
.product-div{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .picture{
    margin-right: 16px;
    img{
      width: 80px;
      height: 80px;
    }
  }
  .product{
    font-family: PingFangSC, PingFang SC;
    font-style: normal;
    .product-key{
      font-weight: 400;
      font-size: 14px;
      color: #90979E;
    }
    .product-value{
      font-weight: 400;
      font-size: 14px;
      color: #52585F ;
    }
    .product-name{
      font-weight: 500;
      font-size: 14px;
      color: #151719;
    }
  }
}

.el-table {
  --el-table-row-hover-bg-color: #F4F6FA !important;
}
.el-table--striped .el-table__body tr.el-table__row--striped.el-table__row--striped.el-table__row--striped td {
  background-color: #F8F9FC; /*替换为你需要的颜色，觉得优先级不够就加!important*/
}
.el-table__row:hover{
  background: #F4F6FA !important;
}
/* 设置当前页面element全局table 选中某行时的背景色*/
.el-table__body tr.current-row>td{
  background: #F4F6FA !important;
}

.el-table th.el-table__cell.is-leaf {
  background: #F4F6FA;
}
.el-table.is-scrolling-none th.el-table-fixed-column--right {
  background-color: #F4F6FA !important;
}

.el-table__header .el-table__cell{
  // 表头样式设置
  background: #F4F6FA !important;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #52585f;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}
.el-table__body .el-table__cell{
  // 表格内容样式设置
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #52585f;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}
.el-table--default .el-table__cell {
  padding: 8px 0 !important;
}
.el-table--default .cell {
  padding: 0 8px !important;
}

.el-table__cell {
  .cell:empty:before {
    content: "-";
  }
}
.el-table{
  .el-checkbox__input.is-checked .el-checkbox__inner:after {
    border-color: var(--el-checkbox-checked-icon-color);
    transform: translate(-45%, -60%) rotate(45deg) scaleY(1);
  }
  .el-checkbox__inner{
    width: 18px !important;
    height: 18px !important;
  }
  .el-checkbox__inner:after{
    left: 50% !important;
    top: 50% !important;
  }
  .el-checkbox__inner:before{
    top: 6px !important;
  }
}


.custom-border{
  width: 100%;
  height: 1px;
  background: #E5E7F3;
}
//  状态样式
.contract {
  &.status {
    border-radius: 2px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    font-style: normal;
    padding: 6px 16px;
    display: inline-block;
    &.cancelled {
      background: rgba(144, 151, 158, 0.1);
      border: 1px solid rgba(200, 201, 204, 0.2);
      color: #90979e;
    }
    &.executing {
      background: rgba(41, 182, 16, 0.08);
      border: 1px solid rgba(41, 182, 16, 0.2);
      color: #29b610;
    }
    &.dateexpired {
      background: rgba(64, 158, 255, 0.08);
      border: 1px solid rgba(64, 158, 255, 0.2);
      color: #409eff;
    }
    &.unwokring {
      background: rgba(255, 156, 0, 0.08);
      border: 1px solid rgba(255, 156, 0, 0.2);
      color: #ff9c00;
    }
    &.failed {
      background: rgba(192,13,29,0.08);
      border: 1px solid rgba(192,13,29,0.2);
      color: #C00C1D
    }
  }
  &.auditStatus{
    border-radius: 2px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding: 6px 16px;
    display: inline-block;
    &.pendingReview{
      color: #ff9c00;
    }
    &.agreed{
      color: #29b610;
    }
    &.rejected{
      color: #C00C1D;
    }
  }
}
.custom-status {
  &.status {
    border-radius: 2px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    font-style: normal;
    padding: 6px 0;
    display: inline-block;
    width: 74px;
    &.cancelled {
      background: rgba(144, 151, 158, 0.1);
      border: 1px solid rgba(200, 201, 204, 0.2);
      color: #90979e;
    }
    &.executing {
      background: rgba(41, 182, 16, 0.08);
      border: 1px solid rgba(41, 182, 16, 0.2);
      color: #29b610;
    }
    &.dateexpired {
      background: rgba(64, 158, 255, 0.08);
      border: 1px solid rgba(64, 158, 255, 0.2);
      color: #409eff;
    }
    &.unwokring {
      background: rgba(255, 156, 0, 0.08);
      border: 1px solid rgba(255, 156, 0, 0.2);
      color: #ff9c00;
    }
    &.failed {
      background: rgba(192,13,29,0.08);
      border: 1px solid rgba(192,13,29,0.2);
      color: #C00C1D
    }
  }
}
.ellipsis{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.align-right{
  text-align: right;
}
.mb-20{
  margin-bottom: 20px!important;
}
.w-100px{
  width: 100px!important;
}
.w-70px{
  width: 70px!important;
}

/* 财务模块el-dialog组件 */
.finance-moudle.el-dialog {
  padding: 0;
  .el-dialog__header {
    padding: 20px 0 20px 30px;
    .el-dialog__title {
      font-weight: 600;
    }
  }
  .el-dialog__body {
    padding: 40px 30px;
    border-top: 1px solid #E5E7F3;
    border-bottom: 1px solid #E5E7F3;
  }
  .el-dialog__footer {
    padding: 12px 30px;
  }
}

/*搜索模块展开收起 公共样式*/
.search-common-expand-toggle-container {
  text-align: center;
  height: 16px;
  .search-common-expand-toggle-icon {
    width: 60px;
    height: 16px;
    line-height: 16px;
    display: inline-block;
    background: #F4F6FA;
    cursor: pointer;
    clip-path: polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%);
  }
}
.expand-common-toggle-container {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  margin-bottom: 0;
  &.expanded {
    height: auto;
    padding: 20px 20px 0;
  }
  &.collapsed {
    height: 0;
    padding: 0 20px;
  }
}
