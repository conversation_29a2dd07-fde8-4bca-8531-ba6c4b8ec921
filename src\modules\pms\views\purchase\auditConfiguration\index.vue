<template>
  <div class="app-container">
    <div class="audit-config-container">
      <!-- 搜索表单卡片 -->
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form :model="searchForm" :inline="true" label-width="84px">
            <el-form-item :label="t('pmsAuditConfiguration.name')">
              <el-input
                v-model="searchForm.nickName"
                :placeholder="t('pmsAuditConfiguration.pleaseInput')"
                maxlength="50"
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item :label="t('pmsAuditConfiguration.mobile')">
              <el-input
                v-model="searchForm.mobile"
                :placeholder="t('pmsAuditConfiguration.pleaseInput')"
                maxlength="20"
                clearable
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="handleSearch"
              >
                {{ t("pmsAuditConfiguration.search") }}
              </el-button>
              <el-button
                @click="handleReset"
              >
                {{ t("pmsAuditConfiguration.reset") }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <!-- 内容卡片 -->
      <el-card class="content-card">
        <!-- 操作按钮区域 -->
        <div class="action-bar">
          <el-button
            type="primary"
            @click="resetAddForm();openDialog()"
          >
            {{ t("pmsAuditConfiguration.addAuditor") }}
          </el-button>
        </div>

        <!-- 主表格 -->
        <el-table :data="tableData" border stripe style="width: 100%" v-loading="loading">
      <el-table-column prop="userName" :label="t('pmsAuditConfiguration.account')" width="120" />
      <el-table-column prop="nickName" :label="t('pmsAuditConfiguration.name')" width="200" />
      <el-table-column prop="mobile" :label="t('pmsAuditConfiguration.mobile')" width="160">
        <template #default="scope">
          <data-marking
              :prefix="scope.row.countryAreaCode.trim()+'-'" 
              :value="scope.row.mobile" 
              type="phone"
              :decrypt-api="decryptApi"
            />
        </template>
      </el-table-column>
      <el-table-column prop="auditRange" :label="t('pmsAuditConfiguration.scope')" />
      <el-table-column prop="deptNames" :label="t('pmsAuditConfiguration.department')" width="" />
      <el-table-column prop="createTime" :label="t('pmsAuditConfiguration.createdAt')" width="180">
        <template #default="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('pmsAuditConfiguration.action')" width="80">
        <template #default="scope">
          <el-button type="text" style="color: #f56c6c" @click="handleDelete(scope.row)">{{ t('pmsAuditConfiguration.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

        <!-- 分页 -->
        <div style="margin: 16px 0; text-align: right">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            :page-sizes="[10, 20, 50]"
            v-model:current-page="page"
            v-model:page-size="pageSize"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 添加审核人弹窗 -->
    <el-drawer v-model="showAddDialog" :title="t('pmsAuditConfiguration.dialogTitle')" width="480px" :close-on-click-modal="false">
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="90px" label-position="top">
        <el-form-item :label="t('pmsAuditConfiguration.selectUser')" prop="userId" required>
          <el-select v-model="addForm.userId" :placeholder="t('pmsAuditConfiguration.pleaseSelect')" filterable style="width:100%">
            <el-option v-for="user in userList" :key="user.userId" :label="user.nickName" :value="user.userId" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('pmsAuditConfiguration.auditScope')" prop="rangeCodos" required>
          <el-checkbox-group v-model="addForm.rangeCodos">
            <el-checkbox value="101" :label="t('pmsAuditConfiguration.puchaseOrder')" />
            <el-checkbox value="102" :label="t('pmsAuditConfiguration.purchaseReturns')" />
            <el-checkbox value="103" :label="t('pmsAuditConfiguration.supplierContract')" />
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">{{ t('pmsAuditConfiguration.cancel') }}</el-button>
        <el-button type="primary" @click="handleAdd">{{ t('pmsAuditConfiguration.confirm') }}</el-button>
      </template>
    </el-drawer>

    <!-- 删除确认弹窗 -->
    <el-dialog v-model="showDeleteDialog" :title="t('pmsAuditConfiguration.prompt')" width="320px" :show-close="false">
      <div>{{ t('pmsAuditConfiguration.confirmDeletePrompt') }}</div>
      <template #footer>
        <el-button @click="showDeleteDialog = false">{{ t('pmsAuditConfiguration.cancel') }}</el-button>
        <el-button type="primary" @click="confirmDelete">{{ t('pmsAuditConfiguration.confirm') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import PurchaseAuditConfigurationAPI from '@/modules/pms/api/auditConfiguration';
import { parseTime } from "@/core/utils";
const { t } = useI18n();

// 筛选表单
const searchForm = reactive({
  nickName: '',
  mobile: ''
});

// 表格数据
const tableData = ref([
]);
const total = ref();
const page = ref(1);
const pageSize = ref(20);
const userList = ref([]);
const loading = ref(false);
function handleSearch() {
  // TODO: 搜索逻辑
  handleQuery();
}

function getUserList() {
  PurchaseAuditConfigurationAPI.getUserList().then(res => {
    userList.value = res;
  });
}
function openDialog() {
  showAddDialog.value = true;
  nextTick(() => {
    addFormRef.value.resetFields();
  });
}
function handleQuery() {
  loading.value = true;
  PurchaseAuditConfigurationAPI.getPage({
    page: page.value,
    limit: pageSize.value,
    nickName: searchForm.nickName,
    mobile: searchForm.mobile
  }).then(res => {
    tableData.value = res.records;
    total.value = Number(res.total);
  }).finally(() => {
    loading.value = false;
  });
}
function handleSizeChange(val) {
  pageSize.value = val;
  handleQuery();
}
function handlePageChange(val) {
  page.value = val;
  handleQuery();
}

// 添加审核人弹窗
const showAddDialog = ref(false);
const addForm = reactive({
  userId: '',
  rangeCodos: []
});
const addFormRef = ref();
const addFormRules = {
  userId: [
    { required: true, message: t('pmsAuditConfiguration.pleaseSelect'), trigger: 'change' }
  ],
  rangeCodos: [
    { required: true, type: 'array', min: 1, message: t('pmsAuditConfiguration.pleaseSelect'), trigger: 'change' }
  ]
};
function handleAdd() {
  addFormRef.value.validate((valid) => {
    if (!valid) return;
    PurchaseAuditConfigurationAPI.update(addForm).then(() => {
      ElMessage.success(t('common.addSuccess'));
      showAddDialog.value = false;
      handleQuery();
    });
  });
}

// 删除确认弹窗
const showDeleteDialog = ref(false);
let deleteRow = null;
function handleDelete(row) {
  deleteRow = row;
  showDeleteDialog.value = true;
}
function confirmDelete() {
  PurchaseAuditConfigurationAPI.delete({
    userId: deleteRow.userId,
  }).then(() => {
    ElMessage.success(t('common.deleteSuccess'));
    showDeleteDialog.value = false;
    handleQuery();
  });
}
function handleReset() {
  page.value = 1;
  pageSize.value = 20;
  searchForm.nickName = '';
  searchForm.mobile = '';
  handleQuery();
}

function resetAddForm() {
  addForm.userId = '';
  addForm.rangeCodos = [];
}
onMounted(() => {
  getUserList();
  handleQuery();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.audit-config-container {
  background: #f5f5f5;
}

.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  padding: 8px 0;
}

.content-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-bar {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.mb-12px {
  margin-bottom: 12px;
}
</style>
