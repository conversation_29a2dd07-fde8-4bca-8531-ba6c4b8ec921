import request from "@/core/utils/request";
const PURCHASE_BASE_URL = "/supply-biz-common/product";

class PurchaseAPI {
    /** 选择页面分页数据 */
    static getPurchaerSelectPage(queryParams?: PurchasePageQuery) {
        return request<any, PageResult<PurchasePageVO[]>>({
            url: `${PURCHASE_BASE_URL}/product/selectPage`,
            method: "post",
            data: queryParams,
        });
    }
  /** 获取采购商品分页数据 */
  static getPurchaerPage(queryParams?: PurchasePageQuery) {
    return request<any, PageResult<PurchasePageVO[]>>({
      url: `${PURCHASE_BASE_URL}/product/page`,
      method: "post",
      data: queryParams,
    });
  }

  /** 导出采购商品 */
  static exportPurchaer(queryParams?: PurchasePageQuery) {
    return request({
      url: `${PURCHASE_BASE_URL}/product/export`,
      method: "post",
      data: queryParams,
    });
  }

  /**批量下架商品 */
  static batchOffShelf(data: UpdateShelvesForm) {
    return request({
      url: `${PURCHASE_BASE_URL}/product/batchOffShelf`,
      method: "post",
      data: data,
    });
  }

  /**批量上架商品 */
  static batchOnShelf(data: UpdateShelvesForm) {
    return request({
      url: `${PURCHASE_BASE_URL}/product/batchOnShelf`,
      method: "post",
      data: data,
    });
  }

  /**单个上架/下架商品 */
  static updateStatus(data: UpdateShelvesForm) {
    return request({
      url: `${PURCHASE_BASE_URL}/product/updateStatus`,
      method: "post",
      data: data,
    });
  }

  /** 根据商品id查询采购商品详情 */
  static queryPurchaseDetail(data: { id?: string }) {
    return request<any, PurchaseFrom>({
      url: `${PURCHASE_BASE_URL}/product/detail`,
      method: "post",
      data: data,
    });
  }

  /** 添加采购商品 */
  static addPurchase(data: PurchaseFrom) {
    return request({
      url: `${PURCHASE_BASE_URL}/product/save`,
      method: "post",
      data: data,
    });
  }

  /** 修改采购商品 */
  static updatePurchase(data: PurchaseFrom) {
    return request({
      url: `${PURCHASE_BASE_URL}/product/update`,
      method: "post",
      data: data,
    });
  }

  /**设置供应商 */
  static setSuppliers(data: { productIds: string[]; supplierId: string[] }) {
    return request({
      url: `${PURCHASE_BASE_URL}/product/batchUpdateProductSupplier`,
      method: "post",
      data: data,
    });
  }

  /**设置默认供应商 */
  static setDefaultSuppliers(data: {
    productIds: string[];
    supplierId: string[];
  }) {
    return request({
      url: `${PURCHASE_BASE_URL}/product/batchUpdateProductDefaultSupplier`,
      method: "post",
      data: data,
    });
  }

  /**
   * 删除采购商品
   *
   */
  static delete(data: { id?: string }) {
    return request({
      url: `${PURCHASE_BASE_URL}/product/delete`,
      method: "post",
      data: data,
    });
  }
  /**
   * 获取商品属性
   *
   */

  static queryProductAttributes(data:any) {
    return request({
      url: `/supply-biz-common/productAttributes/queryPageList`,
      method: "post",
      data,
    });
  }

  /**
   * 根据商品编码查询商品库存与拆装模板
   */
  static queryProductInventoryAndTemplateInfo(data:{ productCode: string}) {
    return request({
      url: `/supply-wms/productStock/queryHasStockAndUse?productCode=${data?.productCode}`,
      method: "get",
    });
  }

  /**
   * 根据商品编码查询商品库存
   */
  static queryProductInventoryInfo(data:{ productCode: string}) {
    return request({
      url: `/supply-wms/productStock/queryHasStock?productCode=${data?.productCode}`,
      method: "get",
    });
  }

  /** 商品批量导入*/
  static importProduct(data?:{file:File}) {
    return request({
      url: `/supply-biz-common/product/product/import`,
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /** 商品导入模板下载*/
  static getProductImportTemplateUrl() {
    return request({
      url: `/supply-biz-common/product/product/queryImportTemplateUrl `,
      method: "get",
    });
  }
}

export default PurchaseAPI;

/** 采购商品分页查询参数 */
export interface PurchasePageQuery extends PageQuery {
  /** 商品名称 */
  productName?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品分类 */
  productCategory?: string;
  /** 商品分类一级 */
  firstCategoryId?: string;
  /** 商品分类二级 */
  secondCategoryId?: string;
  /** 商品分类三级 */
  thirdCategoryId?: string;
  /** 状态 (1->上架，2->下架)*/
  status?: number;
  /** 是否可售卖 (1->是，0->否)*/
  isSalable?: number;
  /** 供应商 */
  supplierId?: string;
}

/** 采购商品分页对象 */
export interface PurchasePageVO {
  /** ID */
  id?: string;
  /** 商品主图片 */
  mainImageUrl?: string;
  /** 商品名称 */
  productName?: string;
  /** 商品编码 */
  productCode?: string;
  /** 规格名称 */
  productSpecName?: string;
  /** 基本单位 */
  conversionRelSecondUnitName?: string;
  /** 产品分类全称 */
  fullCategoryName?: string;
  /** 是否标品：1->是，0->否 */
  isStandard?: number;
  /** 供应商 */
  supplierList?: string;
  /** 创建时间 */
  createTime?: Date;
  /** 状态 1->上架，2->下架*/
  status?: number;
}

/** 采购商品表单对象 */
export interface PurchaseForm {
  /** 人员编码 */
  userCode?: string;
}

/** 采购商品上架下架对象 */
export interface UpdateShelvesForm {
  /** 商品ids */
  productIds?: [string];
  /** 商品id */
  id?: string;
  /** 商品状态 1->上架，2->下架 */
  status?: number;
}

/** 设置供应商弹窗列表分页查询参数 */
export interface SetSupplierPageQuery extends PageQuery {
  /** 供应商名称 */
  supplierName?: string;
}

/** 采购商品对象 */
export interface PurchaseFrom {
  /** ID */
  id?: string;
  /** 商品分类 */
  productCategory?: any;
  /** 商品分类一级 */
  firstCategoryId?: string;
  /** 商品分类二级 */
  secondCategoryId?: string;
  /** 商品分类三级 */
  thirdCategoryId?: string;
  /** 商品名称*/
  productName?: string;
  /** 是否标品*/
  isStandard?: number;
  /** 采购单位id*/
  productUnitId?: string;
  /** 采购单位数量*/
  conversionRelFirstNum?: number;
  /** 基本单位数量*/
  conversionRelSecondNum?: number;
  /** 基本单位数量单位*/
  conversionRelSecondUnitId?: string;
  /** 长*/
  length?: number;
  /** 宽*/
  width?: number;
  /** 高*/
  height?: number;
  /** 体积*/
  volume?: number;
  /** 重量*/
  weight?: number;
  /** 商品品牌 */
  productBrandId?: string;
  /** 保质期 */
  shelfLife?: number;
  /** 保质期单位*/
  shelfLifeUnit?: number;
  /** 损耗比例 */
  lossRatio?: string;
  /** 存储条件 */
  storageCondition?: string;
  /** 备注 */
  remark?: string;
  /** 商品图片 */
  imageUrlList?: string[];
  /** 商品主图 */
  mainImageUrl?: string;
  /** 商品状态 1->上架，2->下架*/
  status?: number;
  /** 供应商列表 */
  supplierList?: supplierList[];
  productCode?: string;
  productDesc?: string;
  attributeType?: number;
  productSpec?: string;
  barcode?: string;
  modelNumber?: string;
  outerProductCode?: string;
  saleAmount?: number;
  saleAmountRadio?: number;
  isSalable?: number;
  imagesUrls?: [];
  fullCategoryName?: string;
  productUnitName?: string;
  conversionRelSecondUnitName?: string;
  productBrandName?: string;
  /** 一级单位增减 */
  isDiscreteUnit?: number;
  /** 计价模式 */
  pricingScheme?: number;
  /** 是否SKU */
  isSku?: boolean;
  /*安全库存*/
  safetyStock?: number;
  /*库存上限*/
  stockUpperLimit?: number;
  /*库存下限*/
  stockLowerLimit?: number;
  /*所属仓库*/
  warehouseName?: string;
  /*所属仓库code*/
  warehouseCode?: string;
}

export interface supplierList {
  /** ID */
  id?: string;
  /** 供应商编码 */
  supplierCode?: string;
  /** 供应商名称*/
  supplierName?: string;
  /** 关联仓库*/
  supplierWarehouseName?: string;
  /** 是否默认（0->否，1->是） */
  isDefault?: number;
}

export interface failedImportList {
  /* 商品字段失败信息*/
  errorMsg?: string;
  /*行数*/
  rowIndex?: number;
}
