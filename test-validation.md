# 快速入库表单验证测试

## 修改内容
修改了快速入库页面的表单验证逻辑，使得当商品数量不填写或者填写为0时，可以不填写其他的必填项也能提交。

## 修改的验证规则
1. **单价 (unitPrice)** - 当数量为0或未填写时，不再必填
2. **入库金额 (amount)** - 当数量为0或未填写时，不再必填
3. **入库库区 (warehouseAreaCode)** - 当数量为0或未填写时，不再必填
4. **产地 (originPlaceId)** - 当数量为0或未填写时，不再必填
5. **入库转换量 (actualInWeight)** - 当数量为0或未填写时，不再必填

## 技术实现
使用条件表达式直接在模板中选择验证规则：

```vue
<!-- 单价验证 -->
:rules="(!scope.row.actualInQty || parseFloat(scope.row.actualInQty) === 0) ? unitPriceOptionalRules : unitPriceRequiredRules"

<!-- 入库金额验证 -->
:rules="(!scope.row.actualInQty || parseFloat(scope.row.actualInQty) === 0) ? amountOptionalRules : amountRequiredRules"

<!-- 库区验证 -->
:rules="(!scope.row.actualInQty || parseFloat(scope.row.actualInQty) === 0) ? [] : warehouseAreaRequiredRules"

<!-- 产地验证 -->
:rules="(!scope.row.actualInQty || parseFloat(scope.row.actualInQty) === 0) ? [] : originPlaceRequiredRules"

<!-- 入库转换量验证 -->
:rules="(!scope.row.actualInQty || parseFloat(scope.row.actualInQty) === 0) ? [] : actualInWeightRequiredRules"
```

## 验证规则定义
```javascript
// 必填规则
const unitPriceRequiredRules = [
  { required: true, message: '单价不能为空', trigger: ['blur', 'change'] },
  { pattern: /^(-?[0-9][0-9]{0,6}(?:\.\d{1,4})?)$/, message: '整数位限长7位，小数后4位', trigger: ['blur', 'change'] }
];

// 可选规则（只有格式验证，无必填验证）
const unitPriceOptionalRules = [
  { pattern: /^(-?[0-9][0-9]{0,6}(?:\.\d{1,4})?)$/, message: '整数位限长7位，小数后4位', trigger: ['blur', 'change'] }
];
```

## 测试场景
1. **场景1**: 商品数量填写大于0的值
   - 预期：所有必填项都需要填写，否则提交时会报错

2. **场景2**: 商品数量不填写（空值）
   - 预期：单价、入库金额、库区、产地、入库转换量都不再必填，可以直接提交

3. **场景3**: 商品数量填写为0
   - 预期：单价、入库金额、库区、产地、入库转换量都不再必填，可以直接提交

4. **场景4**: 动态验证测试
   - 先填写数量为10，观察必填验证
   - 再改为0，观察验证消失
   - 再改回10，观察验证恢复

## 验证方法
1. 访问快速入库页面: http://localhost:3001
2. 添加商品到列表
3. 测试不同的数量输入情况
4. 尝试提交表单，观察验证行为

## 文件位置
`src/modules/wms/views/storeManagement/quickWarehousing/warehousing.vue`

## 修改状态
✅ 已完成 - 使用响应式条件表达式实现动态验证规则
