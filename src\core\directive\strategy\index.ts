import { Directive, DirectiveBinding } from "vue";

/**
 * 策略显示控制指令
 * 用法: v-strategy-show="{ strategyList: strategies, key: 'fieldName' }"
 * 
 * @param strategyList 策略配置列表
 * @param key 要检查的字段key
 */
export const strategyShow: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding;

    if (!value) {
      throw new Error(
        "v-strategy-show需要传入参数! 例: v-strategy-show=\"{ strategyList: strategies, key: 'fieldName' }\""
      );
    }

    const { strategyList, key } = value;

    if (!strategyList || !Array.isArray(strategyList)) {
      console.warn("v-strategy-show: strategyList必须是数组");
      return;
    }

    if (!key) {
      console.warn("v-strategy-show: key参数不能为空");
      return;
    }

    // 检查策略列表中是否存在指定的key
    const strategyItem = strategyList.find(strategy => {
      return strategy.modulePageFieldCode === key;
    });
    const shouldShow = strategyItem?.isShow === 1;
    // 根据策略决定是否显示元素
    if (!shouldShow) {
      el.style.display = 'none';
    } else {
      el.style.display = '';
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    // 当绑定值更新时重新执行逻辑
    const { value } = binding;

    if (!value) return;

    const { strategyList, key } = value;

    if (!strategyList || !Array.isArray(strategyList) || !key) return;
    const strategyItem = strategyList.find(strategy => {
      return strategy.modulePageFieldCode === key;
    });
    const shouldShow = strategyItem?.isShow === 1;
    if (!shouldShow) {
      el.style.display = 'none';
    } else {
      el.style.display = '';
    }
  }
};
