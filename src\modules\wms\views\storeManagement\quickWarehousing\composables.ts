import CommonAPI, { ProductAllPageVO } from "@/modules/wms/api/common";
import { useI18n } from "vue-i18n";

export const useQuickWarehousing = () => {
  const { t } = useI18n();
  // 商品一级单位转二级单位
  async function convertProductUnit(data: any) {
    const res = await CommonAPI.convertProductUnit(data);
    return res?.convertedValue;
  }

  /** 
  一级单位增减 isDiscreteUnit: 1开启，0关闭
  输入若商品的【一级单位增减isDiscreteUnit】为【是】      
    输入数量时，转换量自动转换      
    输入转换量时，数量不管，用户手动填写
  若商品的【一级单位增减】为【否】       
    输入数量时，转换量内无值时自动转换，有值时不转换       
    输入转换量时，数量内无值时自动转换，且数量向上取整，有值时不转换 
  */
  async function convertProductUnitStragery(row: any, stragetry: string) {
    const data = {
      convertUnitTypeEnum: stragetry,
      originalValue: null,
      productId: row.productId || row.id,
      productCode: row.productCode,
    }
    if (stragetry == "FIRST_TO_SECOND") {
      if (row.isDiscreteUnit == 1) {
       if(!isNull(row.actualInQty)){
        data.originalValue = row.actualInQty;
        const val = await convertProductUnit(data);
        row.actualInWeight = parseFloat(val).toFixed(3);
       }
      }
      else if (row.isDiscreteUnit == 0) {
        if (isNull(row.afterInventoryWeight) && !isNull(row.actualInQty)) { // 输入数量时，转换量内无值时自动转换，有值时不转换
          data.originalValue = row.actualInQty;
          const val = await convertProductUnit(data);
          row.actualInWeight = parseFloat(val).toFixed(3);
        }
      }
    }
    else if (stragetry == "SECOND_TO_FIRST") {
      if (row.isDiscreteUnit == 0) {
        if (isNull(row.actualInQty) && !isNull(row.actualInWeight)) { // 输入数量时，转换量内无值时自动转换，有值时不转换
          data.originalValue = row.actualInWeight;
          const val = await convertProductUnit(data);
          row.actualInQty = Math.ceil(val);
        }
      }
    }

    calculateAmount(row);

  }


  // 判断值部位null，undefined，''
  function isNull(val: any) {
    return val === null || val === undefined || val === '';
  }
  // 单价+量+转换量:计算金额
  async function calculateAmount(row: any) {
    if (isNull(row.actualInQty) || isNull(row.actualInWeight) || isNull(row.unitPrice)) return;
    const data = {
      convertedQty: row.actualInWeight,
      productId: row.productId,
      qty: row.actualInQty,
      unitPrice: row.unitPrice,
      productCode: row.productCode,
    }
    const res = await CommonAPI.calculateAmount(data);
    row.amount = res?.amount;
  }

  const receiptTypeList = ref([
    // 入库类型:1:采购入库、2:退货入库、3:调拨入库、4:直接入库、5:地头入库
    {
      key: 1,
      value: t("quickWarehousing.label.purchaseInventory"),
    },
    {
      key: 2,
      value: t("quickWarehousing.label.returnStorage"),
    },
    {
      key: 3,
      value: t("quickWarehousing.label.transferInventory"),
    },
    {
      key: 4,
      value: t("quickWarehousing.label.directStorage"),
    },
    {
      key: 5,
      value: t("quickWarehousing.label.groundStorage"),
    },
  ]);

  // 查找入库类型label
  const filterReceiptTypeLabel = (key: number) => {
    return receiptTypeList.value.find(item => item.key === key)?.value;
  }



  // 1:销售合同 2:采购合同
  const ContractTypeList = ref([
    {
      key: 1,
      value: t("quickWarehousing.label.saleContract"),
    },
    {
      key: 2,
      value: t("quickWarehousing.label.purchaseContract"),
    }
  ])
  // 查找合同类型label
  const filterContractTypeLabel = (key: number) => {
    return ContractTypeList.value.find(item => item.key === key)?.value;
  }

  // 地址过滤函数，如果字段为空则不拼接
  const filterAddress = (data: any, type?: string) => {
    if (type === 'customer') {
      // 客户地址拼接
      const addressParts = [
        data.customerCountryName,
        data.customerProvinceName,
        data.customerCityName,
        data.customerDistrictName,
        data.customerAddress
      ].filter(part => part && part.trim() !== ''); // 过滤空值和空字符串
      
      return addressParts.join('');
    } else {
      // 供应商地址拼接
      const addressParts = [
        data.countryName,
        data.provinceName,
        data.cityName,
        data.districtName,
        data.address
      ].filter(part => part && part.trim() !== ''); // 过滤空值和空字符串
      
      return addressParts.join('');
    }

   
    
  };

   // 获取快速入库策略
  const getStrategyTenantQueryList = async (moduleCode: string, modulePageCode: string) => {
    const res = await CommonAPI.strategyTenantQueryList({
      moduleCode: moduleCode,
      modulePageCode: modulePageCode,
    });
    return res;
  }
  
  return {
    filterAddress,
    filterContractTypeLabel,
    ContractTypeList,
    filterReceiptTypeLabel,
    receiptTypeList,
    convertProductUnit,
    convertProductUnitStragery,
    calculateAmount,
    getStrategyTenantQueryList,
  }
}