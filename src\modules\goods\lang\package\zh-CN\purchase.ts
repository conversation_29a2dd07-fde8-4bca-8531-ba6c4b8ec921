export default {
  purchase: {
    label: {
      productName: "商品名称",
      productCode: "商品编码",
      productCategory: "商品分类",
      status: "商品状态",
      defaults: "默认",
      supplier: "供应商",
      supplierCode: "供应商编码",
      supplierName: "供应商名称",
      product: "商品",
      productSpecName: "规格名称",
      productSpec: "规格",
      conversionRelSecondUnitName: "基本单位",
      isStandard: "是否标品",
      createTime: "创建时间",
      unit: "一级单位",
      supplierCategoryName: "可供商品分类",
      supplierWarehouseName: "关联仓库",
      isStandardCopy: "是否标品",
      conversionRelSecondUnitNameCopy: "采购单位",
      unitNameCopy: "二级单位",
      changeRelationship: "换算关系",
      productlwhv: "商品长宽高",
      length: "长(cm)",
      width: "宽(cm)",
      height: "高(cm)",
      volume: "体积(m³)",
      weight: "重量",
      brand: "商品品牌",
      shelfLife: "保质期",
      lossRatio: "损耗比例",
      storageCondition: "存储条件",
      remark: "备注",
      imagesUrls: "商品图片",
      statusCopy: "商品状态",
      basicInformation: "基本信息",
      graphicInformation: "图文信息",
      supplyChainInformation: "供应链信息",
      productStatusInformation: "商品状态信息",
      repertoryInformation: "库存信息",
      barcode: "条码信息",
      attributeType: "商品属性",
      modelNumber: "商品型号",
      outerProductCode: "外部编码",
      saleInformation: "销售信息",
      productDesc: "商品描述",
      saleAmount: "销售价格",
      saleAmountRadio: "浮动区间",
      isSalable: "是否可售卖",

      color:  "颜色",
      size: "尺码",
      enableNegStock:  "允许负库存",
      stockUpperLimit:  "库存上限",
      stockLowerLimit:  "库存下限",
      safetyStock:  "安全库存",
      isDiscreteUnit: "一级单位增减",
      pricingScheme: "计价模式",
      isSku: "是否SKU",
      warehouseName: "所属仓库",
    },
    placeholder: {
      // keywords: "请输入商品名称/编码",
      productCode: "请输入商品编码",
      productName: "请输入商品名称",
      supplierName: "请输入供应商名称",
      productCategory: "请输入关键词搜索分类",
      weight: "请输入(单位：KG)",
      lossRatio: "请输入0-100%",
      systemGenerated: "系统生成",
      saleAmountRadio: "价格浮动区间：请输入0-100%",
    },
    statusList: {
      haveBeenPutOnShelves: "已上架",
      haveBeenGetOffShelves: "已下架",
    },
    attributeTypeList: {
      finishedGoods: "库存商品",
      auxiliaryMaterials: "辅材",
      packagingMaterials: "包材",
    },
    shelfLifeUnitList: {
      day: "天",
      month: "月",
      year: "年",
    },
    pricingSchemeOptionList: {
      firstLevelUnit: "一级单位",
      secondLevelUnit: "二级单位",
    },
    isSKUOptionList: {
      yes:  "是",
      no: "否",
    },
    button: {
      exportProductInformation: "导出商品信息",
      setSuppliers: "设置供应商",
      setDefaultSuppliers: "设置默认供应商",
      addPurchaseMaterial: "新增采购原料",
      editPurchaseMaterial: "编辑采购原料",
      putOnShelves: "上架",
      getOffShelves: "下架",
      selectDetail: "查看",
      selectSuppliers: "选择供应商",
      addCategory: "新增分类",
      editCategory: "编辑分类",
      addGoods: "新增商品",
      editGoods: "编辑商品",
      close: "关闭",
      batchImport: "导入",
      save: "保存",
    },
    title: {
      selectSuppliers: "选择供应商",
      setSuppliers: "设置供应商",
      setDefaultSuppliers: "设置默认供应商",
      suppliers: "供应商",
      goodsDetails: "商品详情",
      failedImport: "导入失败",
    },
    message: {
      putOnShelvesTips: "是否上架当前选中商品？",
      putOnShelvesCancel: "已取消上架！",
      putOnShelvesSucess: "上架成功！",
      putOnShelvesFail: "上架失败！",
      getOffShelvesTips: "是否下架当前选中商品？",
      getOffShelvesCancel: "已取消下架！",
      getOffShelvesSucess: "下架成功！",
      getOffShelvesFail: "下架失败！",
      deleteTips: "确定删除此商品吗？",
      deleteSupplierTips: "确定删除此供应商吗？",
      deleteConcel: "已取消删除！",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      setSuppliersSucess: "设置供应商成功",
      setSuppliersTips: "只能设置一条供应商",
      setDefaultSuppliersTips: "只能设置一条默认供应商",
      setDefaultSuppliersSucess: "设置默认供应商成功",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      pictureTip: "图片比例1:1,",
      productCodeTips: "请输入大于等于4位的商品编码",
      productNameTips: "请输入大于等于2位的商品名称",
      importProductSuccess: "商品导入成功！",
      importProductFailed: "商品导入失败，请稍后重试！",
      uploadTips: "请上传文件",
      customTips: "导入文件支持xls、xlsx格式,每次导入文件大小不超过2M,数据不超过2000行",
      templateTips: "下载商品管理导入模板.xlsx",
      templateName: '商品管理导入模板.xlsx',
      failedImportTips1: '第',
      failedImportTips2: '行',
    },
    rules: {
      productCategory: "请选择商品分类",
      productName: "请输入商品名称",
      productNameFormat: "商品名称支持2到120个字符",
      isStandard: "请选择是否标品",
      productUnitId: "请选择采购单位",
      productUnit: "请选择一级单位",
      conversionRelFirstNum: "请输入",
      conversionRelFirstNumFormat: "支持小数点前最多14位，小数点后最多2位",
      conversionRelSecondNum: "请输入",
      conversionRelSecondNumFormat:
        "请输入小数点前最多14位，小数点后最多9位的数字",
      conversionRelSecondUnitId: "请选择基本单位",
      unitNameCopy: "请选择二级单位",
      lossRatio: "请输入损耗比例",
      lossRatioFormat: "损耗比例支持0到100的数字，支持小数点后2位",
      shelfLifeFormat: "保质期支持0到50个汉字、英文和数字",
      storageConditionFormat: "存储条件支持0到50个汉字、英文和数字",
      lengthFormat: "请输入大于0且最多8位的整数",
      widthFormat: "请输入大于0且最多8位的整数",
      heightFormat: "请输入大于0且最多8位的整数",
      volumeFormat: "请输入小数点前最多8位，小数点后最多6位的数",
      // weightFormat: "请输入小数点前最多14位，支持小数点后最多3位的数",
      weightFormat: "重量支持小数点前最多14位，小数点后最多3位",
      status: "请选择商品状态",
      imagesUrls: "请上传商品图片",
      saleAmount: "请输入销售价格",
      saleAmountRadio: "请输入浮动区间",
      weight: "请输入重量",
      saleAmountFormat: "销售价格支持大于等于0的数字，支持小数点前8位，支持小数点后2位",
      saleAmountRadioFormat: "浮动区间支持0到100的数字，支持小数点后2位",
      isDiscreteUnit:  "请选择一级单位增减",
      pricingScheme: "请选择计价模式",
      isSku:  "请选择是否SKU",
      safetyStockFormat: "请输入大于等于0的数字，支持小数点前8位，小数点后3位",
    },
  },
};
