<template>
  <el-drawer
    v-model="dialogVisible"
    title="质检商品"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <div class="quality-check-content">
      <!-- 商品信息 -->
      <div class="product-info-section mb-4">
        <div class="section-title">商品信息</div>
        <el-form label-width="120px" class="product-form">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="商品编码：">
                <div>{{ productData.productCode || '--'}}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="商品名称：">
                <div>{{ productData.productName || '--' }}</div>
              </el-form-item>
            </el-col>
          <!-- </el-row>
          <el-row :gutter="20"> -->
            <el-col :span="8">
              <el-form-item label="规格：">
                <div>{{ productData.productSpecs || '--' }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="入库量：">
                <el-input :value="`${productData.actualInQty || 0}`" :disabled="true">
                  <template #append>{{ productData.productUnitName }}</template>
                </el-input>
              </el-form-item>
            </el-col>
          <!-- </el-row>
          <el-row :gutter="20"> -->
            <el-col :span="12">
              <el-form-item label="转换量：">
                <el-input :value="`${productData.actualInWeight || 0}`" :disabled="true" >
                  <template #append>{{ productData.conversionRelSecondUnitName }}</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 质检表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="80px"
      >
        <!-- 动态规格表格 -->
        <el-table
          :data="formData.detailList"
          border
          size="small"
          class="spec-table"
          :show-summary="true"
          :summary-method="getSummaries"
        >
          <el-table-column label="规格" min-width="150">
            <template #default="{ row }">
              <el-input
                v-model="row.specification"
                placeholder="请输入"
                :disabled="type === 'show'"
              />
            </template>
          </el-table-column>
          
          <el-table-column label="数量" min-width="150" align="center">
            <template #default="{ row, $index }">
              <!-- @change="calculateProportion($index)" -->
              <el-input-number
                v-model="row.quantity"
                :precision="2"
                :min="0"
                controls-position="right"
                style="width: 100%"
                :disabled="type === 'show'"
              />
            </template>
          </el-table-column>
          
          <el-table-column label="占比" min-width="150" align="center">
            <template #default="{ row }">
              <el-input
                v-model="row.proportion"
                placeholder="0.00~100"
                :disabled="type === 'show'"
              >
                <template #append>%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150" align="center">
            <template #default="{ row }">
              <el-input
                v-model="row.remark"
                placeholder="请输入备注"
              />
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150" align="center" fixed="right" v-if="type === 'update'">
            <template #default="{ $index }">
              <el-button
                type="primary"
                @click="handleAddRow($index)"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
              <el-button
                type="danger"
                @click="handleRemoveRow($index)"
              >
                <el-icon><Minus /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 扣款信息 -->
        <el-row :gutter="20" class="mt-4">
          <el-col :span="12">
            <el-form-item label="扣款金额" prop="deductionAmount">
              <el-input-number
                v-model="formData.deductionAmount"
                :precision="2"
                controls-position="right"
                class="w-full"
                :disabled="type === 'show'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扣款说明" prop="deductionRemark">
              <el-input
                v-model="formData.deductionDesc"
                placeholder="请输入"
                :disabled="type === 'show'"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 附件上传 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="附件" prop="attachment">
              <UploadMultiple
                :modelValue="formData.deductionAttachment"
                v-model="formData.deductionAttachment"
                @update:model-value="handleFileUpload"
                :limit="6"
                :fileSize="10"
                :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx', 'png', 'jpg', 'jpeg']"
                listType="text"
                :showUploadBtn="type !== 'show'"
                :isShowTip="type !== 'show'"
                :isDelete="true"
                customTips="支持上传PDF、Word、Excel、图片等格式文件，单个文件不超过10MB"
                class="modify-multipleUpload"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 底部按钮 -->
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="submitting">
          确定
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import { Plus, Minus, Upload } from "@element-plus/icons-vue";
import UploadMultiple from "@/core/components/Upload/UploadMultiple.vue";

interface Props {
  visible: boolean;
  productData: any;
  type: string
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "confirm", data: any): void;
}

interface QualityDetailItem {
  specification: string;
  quantity: number | null;
  proportion: number | null;
  proportionDisplay: string;
  remark: string;
}

interface QualityFormData {
  detailList: QualityDetailItem[];
  deductionAmount: number | null; // 扣款金额
  deductionDesc: string; // 扣款说明
  deductionAttachment: string; // 扣款附件 
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  productData: () => ({}),
  type: 'update',
});

const emit = defineEmits<Emits>();

const formRef = ref();
const submitting = ref(false);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 表单数据
const formData = reactive<QualityFormData>({
  detailList: [
    {
      specification: "",
      quantity: null,
      proportion: null,
      proportionDisplay: "",
      remark: "1",
    },
  ],
  deductionAmount: null, // 扣款金额
  deductionDesc: "", // 扣款说明
  deductionAttachment: "", // 扣款附件 
});

// 表单验证规则
const rules = {
  detailList: [
    { required: true, message: "请填写规格明细", trigger: "blur" },
  ],
};

// 计算属性
const totalQuantity = computed(() => {
  return formData.detailList.reduce((sum, item) => sum + (item.quantity || 0), 0);
});

const totalUnit = computed(() => {
  return "kg"; // 根据实际需求设置单位
});

// 监听产品数据变化
watch(
  () => props.productData,
  (newData) => {
    if (newData && Object.keys(newData).length > 0) {
      // 初始化第一行数据
      if (formData.detailList.length > 0) {
       /*  formData.detailList[0].specification = newData.specifications || "";
        formData.detailList[0].quantity = newData.quantity || 0;
        formData.detailList[0].proportion = newData.proportion || 0;
        formData.detailList[0].proportionDisplay = newData.proportionDisplay || ""; */
        formData.detailList = newData.detailList || [];
        formData.deductionAmount = newData.deductionAmount || null;
        formData.deductionDesc = newData.deductionDesc || "";
        formData.deductionAttachment = newData.deductionAttachment || "";
        // calculateProportion(0);
      }
    }
  },
  { immediate: true, deep: true }
);

// 计算占比
const calculateProportion = (index: number) => {
  const item = formData.detailList[index];
  const total = totalQuantity.value;
  
  if (total > 0 && item.quantity > 0) {
    item.proportion = (item.quantity / total) * 100;
    item.proportionDisplay = item.proportion.toFixed(2);
  } else {
    item.proportion = 0;
    item.proportionDisplay = "0.00";
  }
  
  // 重新计算所有占比
  formData.detailList.forEach((detailItem, idx) => {
    if (idx !== index && total > 0 && detailItem.quantity > 0) {
      detailItem.proportion = (detailItem.quantity / total) * 100;
      detailItem.proportionDisplay = detailItem.proportion.toFixed(2);
    }
  });
};

// 添加行
const handleAddRow = (index: number) => {
  formData.detailList.splice(index + 1, 0, {
    specification: "",
    quantity: null,
    proportion: null,
    proportionDisplay: "",
    remark: "",
  }); 
};

// 删除行
const handleRemoveRow = (index: number) => {
  if (formData.detailList.length >= 1) {
    formData.detailList.splice(index, 1);
    // 重新计算所有占比
    formData.detailList.forEach((_, idx) => {
      calculateProportion(idx);
    });
  }
};

// 表格合计方法
const getSummaries = (param: any) => {
  const { columns, data } = param;
  const sums: string[] = [];
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计';
    } else if (index === 1) {
      // 数量列
      const values = data.map((item: any) => Number(item.quantity));
      if (!values.every((value: number) => Number.isNaN(value))) {
        sums[index] = values.reduce((prev: number, curr: number) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0).toFixed(2);
      } else {
        sums[index] = '0.00';
      }
    } else {
      sums[index] = '';
    }
  });
  
  return sums;
};

// 文件上传处理
const handleFileUpload = (files: any[]) => {
  // formData.deductionAttachment = files;
  if (files && files.length > 0) {
    formData.deductionAttachment = JSON.stringify(files);
  } else {
    formData.deductionAttachment = "";
  }
};

const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};


const handleConfirm = async () => {
  try {
    await formRef.value.validate();
    
    submitting.value = true;
    // 构造返回数据，符合 ReceiptNoticeQcProductDetail 接口
    const result = {
      // id: 0,
      // receiptNoticeId: 0,
      productCode: props.productData.productCode,
      detailList: formData.detailList.map(item => ({
        specification: item.specification,
        quantity: item.quantity,
        proportion: item.proportion,
        remark: item.remark
      })),
      deductionAmount: formData.deductionAmount,
      deductionDesc: formData.deductionDesc,
      deductionAttachment: formData.deductionAttachment,
      index: props.productData.index,
    };
    
    emit("confirm", result);
    handleClose();
    
    // ElMessage.success("质检完成");
  } catch (error) {
    // console.error("质检失败:", error);
  } finally {
    submitting.value = false;
  }
};

const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    detailList: [
      {
        specification: "",
        quantity: null,
        proportion: null,
        proportionDisplay: "",
      },
    ],
    deductionAmount: null,
    deductionDesc: "",
    deductionAttachment: "",
  });
};
</script>

<style scoped>
.quality-check-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-info-section {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.product-form {
  background: white;
  padding: 16px;
  border-radius: 4px;
}

.spec-table {
  margin-bottom: 20px;
}

.spec-table .el-button {
  margin-right: 4px;
}

.spec-table .el-button:last-child {
  margin-right: 0;
}



.drawer-footer {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 16px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: auto;
  text-align: right;
}

.drawer-footer .el-button {
  margin-left: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .product-header,
  .product-details {
    flex-direction: column;
    gap: 8px;
  }
  
  .quality-check-content {
    padding: 16px;
  }
  
  .spec-table .el-table__body-wrapper {
    overflow-x: auto;
  }
}

/* 表单样式优化 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
}

/* 只读输入框样式 */
:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  color: #495057;
}

:deep(.product-form .el-input[readonly] .el-input__inner) {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  color: #495057;
  cursor: default;
}
</style> 