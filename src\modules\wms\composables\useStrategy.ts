import { ref } from 'vue'
import CommonAPI from '@/modules/wms/api/common'

/**
 * 策略管理 composable
 * 用于处理页面字段显示策略
 */
export function useStrategy() {
  const strategyList = {}
  const loading = ref(false)

  /**
   * 获取策略列表
   * @param moduleCode 模块编码
   * @param modulePageCode 页面编码
   */
  const getStrategyList = async (moduleCode: string, modulePageCode: string) => {
    loading.value = true
    try {
      const res = await CommonAPI.strategyTenantQueryList({
        moduleCode,
        modulePageCode,
      })
      strategyList[moduleCode] = (res as any) || []
    } catch (error) {
      console.error('获取策略列表失败:', error)
      strategyList[moduleCode] = []
    } finally {
      loading.value = false
    }
  }

  /**
   * 检查字段是否应该显示
   * @param key 字段标识
   * @param defaultShow 默认是否显示（当策略不存在时）
   */
  const checkFieldShow = (moduleCode: string, key: string, defaultShow: boolean = true): boolean => {
    if (!strategyList[moduleCode] || !Array.isArray(strategyList[moduleCode]) || !key) {
      return defaultShow
    }
    
    const strategyItem = strategyList[moduleCode].find(strategy => {
      return strategy.modulePageFieldCode === key
    })
    
    // 如果找不到策略配置，使用默认值
    if (!strategyItem) {
      return defaultShow
    }
    
    return strategyItem.isShow === 1
  }


  return {
    strategyList,
    loading,
    getStrategyList,
    checkFieldShow,
  }
}

