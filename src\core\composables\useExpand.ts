
export const useExpand = (className: string) => {
  const isExpand = ref(true);
  const targetDom = document.getElementsByClassName(className)
  const handleExpand = () => {
    isExpand.value = !isExpand.value;
    if(!targetDom?.length){ return }
    const element= targetDom[0]
    if (isExpand.value) {
      element.classList.remove('collapsed');
      element.classList.add('expanded');
    } else {
      element.classList.remove('expanded');
      element.classList.add('collapsed');
    }
  }

  // 初始化样式
  onMounted(() => {
    if (targetDom?.length) {
      const element = targetDom[0];
      element.classList.add('expand-common-toggle-container');
      if (isExpand.value) {
        element.classList.add('expanded');
      } else {
        element.classList.add('collapsed');
      }
    }
  });

  return {
    isExpand,
    handleExpand,
  };
};
