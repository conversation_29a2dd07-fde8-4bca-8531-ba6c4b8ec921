<template>
  <div class="app-container">
    <div
      class="addOrder"
      v-loading="loading"
    >
      <div class="page-title">
        <div
          @click="handleBack()"
          class="cursor-pointer mr8px"
        >
          <el-icon><Back /></el-icon>
        </div>
        <div>
          <span v-if="type == 'add' || type == 'copy'">
            {{ $t("omsOrder.button.addOrder") }}
          </span>
          <span v-else>{{ $t("omsOrder.button.adjustOrder") }}</span>
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          :rules="rules"
          ref="fromRef"
          label-width="108px"
          label-position="right"
        >
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("omsOrder.label.basicInformation") }}
            </div>
          </div>
          <div>
            <el-row class="flex-center-start">
              <!--销售类型-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.orderType')" prop="orderType">
                  <el-select
                    v-model="form.orderType"
                    @change="orderTypeChange"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                  >
                    <el-option
                      v-for="item in orderTypeOptionList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 兑换卡号-->
              <el-col :span="8" v-if="form.orderType == 2">
                <el-form-item :label="$t('omsOrder.label.exchangeCode')" prop="exchangeCode">
                  <el-input
                    v-model="form.exchangeCode"
                    :placeholder="$t('omsOrder.placeholder.exchangeCode')"
                    :maxlength="30"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <!--原订单号-->
              <el-col :span="8" v-if="form.orderType == 4">
                <el-form-item :label="$t('omsOrder.label.originalOrderCode')" prop="originalOrderCode">
                  <el-select
                    v-model="form.originalOrderCode"
                    :placeholder="$t('omsOrder.placeholder.originalOrderCode')"
                    clearable
                    filterable
                    remote
                    remote-show-suffix
                    :remote-method="originalOrderCodeChange"
                    :loading="loading"
                  >
                    <el-option
                      v-for="item in oldOrderCodeOptionData"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--销售主题-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.orderTheme')" prop="orderTheme">
                  <el-input
                    v-model="form.orderTheme"
                    :placeholder="$t('common.placeholder.inputTips')"
                    :maxlength="100"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <!--客户属性-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.customerAttributes')" prop="customerAttributes">
                  <el-radio-group @change="customerAttributesChange" v-model="form.customerAttributes">
                    <el-radio :value="1">
                      {{ $t("omsOrder.label.bigCustomer") }}
                    </el-radio>
                    <el-radio :value="2">
                      {{ $t("omsOrder.label.smallCustomer") }}
                    </el-radio>
                    <el-radio :value="3">
                      {{ $t("omsOrder.label.otherCustomer") }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <!--客户-->
              <el-col :span="8" v-if="form.customerAttributes == 1">
                <el-form-item :label="$t('omsOrder.label.customerNameCopy')" prop="customerCode">
                  <el-select
                    v-model="form.customerCode"
                    :placeholder="$t('omsOrder.placeholder.customerName')"
                    clearable
                    filterable
                    :disabled="type == 'check'"
                    @change="customerChange"
                  >
                    <el-option
                      v-for="item in customerList"
                      :key="item.customerCode"
                      :label="item.customerName"
                      :value="item.customerCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--客户合同-->
              <el-col :span="8" v-if="form.customerAttributes == 1">
                <el-form-item :label="$t('omsOrder.label.contract')" prop="contractId">
                  <el-select
                    v-model="form.contractId"
                    :placeholder="$t('omsOrder.placeholder.contract')"
                    clearable
                    @change="contractChange"
                  >
                    <el-option
                      v-for="item in contractList"
                      :key="item.contractId"
                      :label="item.contractName"
                      :value="item.contractId"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--结算方式-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.paymentType')" prop="paymentType">
                  <el-select
                    v-model="form.paymentType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    @change="paymentTypeChange"
                  >
                    <el-option
                      v-for="item in paymentTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--审核人员-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.approveUserName')" prop="approveUser">
                  <el-select
                    v-model="form.approveUser"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    @change="approveUserChange"
                    filterable
                  >
                    <el-option
                      v-for="item in approvePersonList"
                      :key="item.userId"
                      :value="item.userId"
                      :label="item.nickName"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--销售人员-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.salesName')" prop="salesId">
                  <el-select
                    v-model="form.salesId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    @change="saleUserChange"
                    filterable
                  >
                    <el-option
                      v-for="item in salePersonList"
                      :key="item.userId"
                      :value="item.userId"
                      :label="item.nickName"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--经办人-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.operatorUser')" prop="handlerUserId">
                  <el-select
                    v-model="form.handlerUserId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    @change="handlerUserChange"
                    filterable
                  >
                    <el-option
                      v-for="item in operatorUserList"
                      :key="item.userId"
                      :value="item.userId"
                      :label="item.nickName"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--预售-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.presale')" prop="isPresale">
                  <el-radio-group v-model="form.isPresale">
                    <el-radio :value="1">
                      {{ $t("omsOrder.presaleList[1]") }}
                    </el-radio>
                    <el-radio :value="0">
                      {{ $t("omsOrder.presaleList[0]") }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <!-- 期望出库时间-->
              <el-col :span="16" class="flex-center-start">
                <el-form-item :label="$t('omsOrder.label.planDeliveryDateCopy')" prop="planDeliveryDate">
                  <el-date-picker
                    :editable="false"
                    v-model="form.planDeliveryDate"
                    type="date"
                    value-format="YYYY-MM-DD"
                    :disabled="type == 'check'"
                    class="!w-[256px] mr20px"
                    :placeholder="$t('common.placeholder.selectTips')"
                  />
                </el-form-item>
                <el-form-item label-width="0px" prop="planDeliveryTime">
                  <el-time-picker
                    v-model="form.planDeliveryTime"
                    is-range
                    :disabled="type == 'check'"
                    value-format="HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    class="!w-[256px]"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="line"></div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("omsOrder.label.deliveryInformation") }}
            </div>
          </div>
          <div>
            <el-row class="flex-center-start">
              <el-col :span="8">
                <!--收货人姓名-->
                <el-form-item :label="$t('omsOrder.label.receiveNameCopy')" prop="contactPerson">
                  <el-input
                    v-model="form.contactPerson"
                    :placeholder="$t('common.placeholder.inputTips')"
                    :maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!--手机号码-->
                <el-form-item :label="$t('omsOrder.label.receiveMobileCopy')" prop="contactMobile">
                  <el-input
                    v-model="form.contactMobile"
                    :placeholder="$t('common.placeholder.inputTips')"
                    :maxlength="30"
                    clearable
                  >
                    <template #prepend>
                      <el-select
                        v-model="form.contactAreaCode"
                        style="width: 80px"
                      >
                        <el-option
                          v-for="(item, index) in countryNumCodeList"
                          :key="index"
                          :label="item.internationalCode"
                          :value="item.internationalCode"
                        />
                      </el-select>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="flex-center-start">
                <el-form-item :label="$t('omsOrder.label.deliveryAddressCopy')" prop="areaInfo">
                  <!-- 国家 -->
                  <el-select
                    v-model="form.countryId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="countryChange"
                    class="!w-[100px]"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in countryList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- 省 -->
                  <el-select
                    v-if="showProvinceInput"
                    :disabled="!form.countryId"
                    v-model="form.provinceId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleProvinceChange"
                    class="!w-[100px]"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in provinceList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- 市 -->
                  <el-select
                    v-if="showCityInput"
                    v-model="form.cityId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleCityChange"
                    class="!w-[100px]"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in cityList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- 区 -->
                  <el-select
                    v-if="showDistrictInput"
                    v-model="form.districtId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleDistrictChange"
                    class="!w-[100px]"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in districtList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('omsOrder.label.address')" prop="address">
                  <el-input
                    v-model="form.address"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="100"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="line"></div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("omsOrder.label.warehouseDelivery") }}
            </div>
          </div>
          <div>
            <el-row class="flex-center-start">
              <!--仓库-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.warehouse')" prop="warehouseId">
                  <el-select
                    v-model="form.warehouseId"
                    @change="warehouseChange"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="item in warehouseList"
                      :key="item.warehouseId"
                      :label="item.warehouseName"
                      :value="item.warehouseId"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--配送方式-->
              <el-col :span="8">
                <el-form-item :label="$t('omsOrder.label.deliveryMethod')" prop="deliveryType">
                  <!--级联功能保留，后续统一替换-->
                  <!--                          <el-cascader v-model="form.deliveryType" clearable :props="deliveryMethodProps" @change="deliveryMethodChange"  :placeholder="$t('omsOrder.placeholder.deliveryMethod')" />-->
                  <el-select
                    v-model="form.deliveryType"
                    @change="deliveryMethodChange"
                    :placeholder="$t('omsOrder.placeholder.deliveryMethod')"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="item in deliveryTypeList"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable" style="justify-content: space-between">
            <div class="title-content">
              {{ $t("omsOrder.label.productListInformation") }}
            </div>
            <div class="button-add cursor-pointer" @click="addProduct()">
              {{ $t("omsOrder.button.addProduct") }}
            </div>
          </div>
          <div>
            <el-table
              class="table-div"
              :data="form.orderDetailDTOList"
              highlight-current-row
              stripe
            >
              <el-table-column type="index" :label="$t('common.sort')" width="60"/>
              <el-table-column :label="$t('omsOrder.label.product')" min-width="150" show-overflow-tooltip>
                <template #default="scope">
                  <div class="product-div">
                    <div class="product">
                      <div class="product-name">
                        {{ scope.row.productName }}
                      </div>
                      <div>
                        <span class="product-key">
                          {{ $t("omsOrder.label.productCode") }}：
                        </span>
                        <span class="product-value">
                          {{ scope.row.productCode }}
                        </span>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('omsOrder.label.productSpecName')" prop="productSpecs" show-overflow-tooltip/>
              <el-table-column :label="'*' + $t('omsOrder.label.saleCount')" width="350px">
                <template #default="scope">
                  <el-form-item
                    class="mb0px mt18px"
                    label-width="0px"
                    :prop="'orderDetailDTOList.' + scope.$index + '.saleCount'"
                    :rules="
                      dialog.visible
                        ? []
                        : [
                            {
                              required: true,
                              message: t('omsOrder.rules.saleCount'),
                              trigger: 'blur',
                            },
                            {
                              pattern:
                                /(^[1-9](\d{0,10})?(\.\d{1,2})?$)|(^0\.[1-9]\d{0,1}$)|(^0\.0[1-9]$)/,
                              message: t('omsOrder.rules.saleCountFomart'),
                              trigger: 'blur',
                            },
                          ]
                    "
                  >
                    <el-input
                      @change="saleChange(scope.$index, 1)"
                      v-model="scope.row.saleCount"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column :label="'*' + $t('omsOrder.label.salePrice')" align="right" width="300px">
                <template #default="scope">
                  <el-form-item
                    class="mb0px mt18px"
                    label-width="0px"
                    :prop="'orderDetailDTOList.' + scope.$index + '.salePrice'"
                    :rules="
                      dialog.visible
                        ? []
                        : [
                            {
                              required: true,
                              message: t('omsOrder.rules.salePrice'),
                              trigger: 'blur',
                            },
                            {
                              pattern:
                                /^(-?0(?:\.\d{1,4})?|[1-9]\d{0,6}(?:\.\d{1,4})?)$/,
                              message: t('omsOrder.rules.salePriceFomart'),
                              trigger: 'blur',
                            },
                            {
                              required: true,
                              validator: validatorSalePrice,
                              trigger: ['blur', 'change'],
                            },
                          ]
                    "
                  >
                    <el-input
                      @change="saleChange(scope.$index, 1)"
                      v-model="scope.row.salePrice"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                    >
                      <template #prefix>
                        <span v-if="form.currencyCode == 'CNY'">￥</span>
                        <span v-else>$</span>
                      </template>
                    </el-input>
                  </el-form-item>
                  <!--                                    <template v-if="scope.row.floatRange">-->
                  <!--                                        <el-tooltip effect="dark" :content="$t('omsOrder.label.floatRange')+'【'+ `${form.currencyCode== 'CNY'?'￥':'$'}` +`${scope.row.min}`+'~'+ `${form.currencyCode== 'CNY'?'￥':'$'}` +`${scope.row.max}` + '】'" placement="top">-->
                  <!--                                            <div style="position: absolute;right: 0" v-if="scope.row.floatRange">-->
                  <!--                                                {{$t('omsOrder.label.floatRange')}}-->
                  <!--                                                &lt;!&ndash; {{scope.row.floatRange}}%&ndash;&gt;-->
                  <!--                                                【-->
                  <!--                                                <span v-if="form.currencyCode == 'CNY'">￥</span>-->
                  <!--                                                <span v-else>$</span>-->
                  <!--                                                {{scope.row.min}}-->
                  <!--                                                ~-->
                  <!--                                                <span v-if="form.currencyCode == 'CNY'">￥</span>-->
                  <!--                                                <span v-else>$</span>-->
                  <!--                                                {{scope.row.max}}-->
                  <!--                                                】-->
                  <!--                                            </div>-->
                  <!--                                        </el-tooltip>-->
                  <!--                                    </template>-->
                </template>
              </el-table-column>
              <el-table-column :label="'*' + $t('omsOrder.label.total')" show-overflow-tooltip align="right">
                <template #default="scope">
                  <span v-if="scope.row.saleMoney || scope.row.saleMoney == 0">
                    <span v-if="form.currencyCode == 'CNY'">￥</span>
                    <span v-else>$</span>
                    {{ scope.row.saleMoney }}
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('omsOrder.label.remark')" width="300px" show-overflow-tooltip>
                <template #default="scope">
                  <el-input
                    type="textarea"
                    show-word-limit
                    v-model="scope.row.remark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="100"
                    clearable
                  />
                </template>
              </el-table-column>
              <el-table-column fixed="right" :label="$t('common.handle')" width="80">
                <template #default="scope">
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click="handleDelete(scope.$index)"
                  >
                    {{ $t("common.delete") }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="table-title">
              <!--合计-->
              <div><span>{{ t("omsOrder.label.total") }}：</span></div>
              <div>
                <!-- 商品个数-->
                <span class="mr16px">{{ t("omsOrder.label.productCount") }}：<span style="font-size: 18px; color: #c00c1d">{{ form.orderDetailDTOList.length }}</span></span>
                <!--货品合计金额-->
                <span>
                  {{ t("omsOrder.label.totalMoney") }}：
                  <span style="font-size: 18px; color: #c00c1d">
                    <template v-if="form.orderDetailDTOList && form.orderDetailDTOList.length > 0">
                      <span v-if="form.currencyCode == 'CNY'">￥</span>
                      <span v-else>$</span>
                    </template>
                    {{ form.totalAmount }}
                  </span>
                </span>
              </div>
            </div>
            <div class="mt25px">
              <el-row>
                <el-col :span="8">
                  <!--商品总金额-->
                  <el-form-item :label="$t('omsOrder.label.productTotalAmount')" prop="totalAmountCopy">
                    <el-input
                      v-model="form.totalAmountCopy"
                      :placeholder="$t('common.placeholder.inputTips')"
                      @change="totalAmountCopyChange"
                      clearable
                    >
                      <template #prefix>
                        <span v-if="form.currencyCode == 'CNY'">￥</span>
                        <span v-else>$</span>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="flex-center-start">
                  <!--优惠方式-->
                  <el-form-item :label="$t('omsOrder.label.discountType')" prop="discountType">
                    <el-radio-group @change="changeDiscountType" v-model="form.discountType">
                      <el-radio :value="1">
                        {{ $t("omsOrder.discountTypeList[1]") }}
                      </el-radio>
                      <el-radio :value="2">
                        {{ $t("omsOrder.discountTypeList[2]") }}
                      </el-radio>
                      <el-radio :value="3">
                        {{ $t("omsOrder.discountTypeList[3]") }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item class="ml25px" v-if="form.discountType == 2" label-width="0px" prop="discountAmount">
                    <el-input
                      v-model="form.discountAmount"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                    >
                      <template #prefix>
                        <span v-if="form.currencyCode == 'CNY'">￥</span>
                        <span v-else>$</span>
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item class="ml25px" v-if="form.discountType == 3" label-width="0px" prop="discountValue">
                    <el-input-number
                      v-model="form.discountValue"
                      :placeholder="$t('common.placeholder.inputTips')"
                      :min="0.01"
                      :max="100"
                      :precision="2"
                      :controls="false"
                    />
                    <span>%</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!--优惠后金额-->
                  <el-form-item :label="$t('omsOrder.label.totalDiscountedAmount')" prop="totalDiscountedAmount">
                    <el-input
                      v-model="form.totalDiscountedAmount"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                    >
                      <template #prefix>
                        <span v-if="form.currencyCode == 'CNY'">￥</span>
                        <span v-else>$</span>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("omsOrder.label.remarkInformation") }}
            </div>
          </div>
          <div>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('omsOrder.label.remark')" prop="remark">
                  <el-input
                    :rows="4"
                    type="textarea"
                    show-word-limit
                    v-model="form.remark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="200"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">{{ $t("omsOrder.label.orderAttachmentFilesInformation") }}</div>
          </div>
          <div>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('omsOrder.label.orderAttachmentFiles')" prop="orderAttachmentFiles">
                  <upload-multiple
                    :listType="`text`"
                    :tips="''"
                    :fileSize="20"
                    :fileType="['jpg', 'jpeg', 'png', 'pdf', 'zip', 'rar']"
                    :isPrivate="`public-read`"
                    :modelValue="form.orderAttachmentFiles"
                    @update:model-value="onChangeMultiple"
                    ref="detailPicsRef"
                    :limit="10"
                    :formRef="formUpdateRef"
                    class="modify-multipleUpload"
                    name="detailPic"
                  >
                    <template #default="{ file }">点击上传</template>
                  </upload-multiple>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleBack">{{ $t("common.reback") }}</el-button>
        <el-button v-if="type == 'check'" type="primary" :loading="submitLoading" @click="handleSubmit(3)">
          {{ $t("omsOrder.button.saveCheck") }}
        </el-button>
        <!--保存草稿-->
        <el-button v-if="type !== 'check'" type="primary" plain :loading="submitLoading" @click="handleSubmit(0)">
          {{ $t("omsOrder.button.saveDraft") }}
        </el-button>
        <!--提交订单-->
        <el-button v-if="type !== 'check'" type="primary" :loading="submitLoading" @click="handleSubmit(1)">
          {{ $t("omsOrder.button.submitOrder") }}
        </el-button>
      </div>
      <AddProduct
        ref="addProductRef"
        v-model:visible="dialog.visible"
        :isOrderPageFlag="true"
        :title="dialog.title"
        @onSubmit="onSubmit"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "OmsAddOrder",
  inheritAttrs: false,
});

import CommonAPI from "@/modules/wms/api/common";
import AddProduct from "./components/addProduct.vue";
import {
  parseDateTime,
  convertToTimestamp,
  isEmpty,
} from "@/core/utils/index.js";
import { querySalesPersonUser } from "@/modules/oms/api/contract";
import { useRoute, useRouter } from "vue-router";
import OrderAPI, {
  OrderFrom,
  ProductAllPageVO,
  ProductAllPageQuery,
} from "@/modules/oms/api/order";
import UserAPI from "@/core/api/accountManagement.ts";
import CustomerApi from "@/modules/oms/api/customer";
import { useUserStore } from "@/core/store";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore()
const countryNumCodeList = ref([]);
const countryList = ref([]);
const provinceList = ref([]);
const cityList = ref([]);
const districtList = ref([]);
const showProvinceInput = ref(false);
const showCityInput = ref(false);
const showDistrictInput = ref(false);
const { t } = useI18n();
const fromRef = ref();
const submitLoading = ref(false);
const loading = ref(false);
const id = route.query.id;
const orderCode = route.query.orderCode;
const deliveryScheduleDelete = route.query.deliveryScheduleDelete || "no";
const type = route.query.type;
const addProductRef = ref();
const formUpdateRef = ref(null);
const showDisabled = ref(false);
const confirmDisabled = ref(false);
const disabledDate = (time) => {
  // 获取今天的日期
  const today = new Date();
  today.setHours(0, 0, 0, 0); // 将时间设置为0点，确保比较时不考虑具体时间
  // 比较传入的日期是否小于今天
  return time.getTime() < today.getTime();
};
const dialog = reactive({
  title: "",
  visible: false,
});
/*销售类型 1: 普通销售 2: 提货卡兑换 3: 业务招待 4: 补发 5: 地头售卖 9: 参展 */
const orderTypeOptionList = ref([
  {
    value: 1,
    label: t("omsOrder.orderTypeList[1]"),
  },
  {
    value: 2,
    label: t("omsOrder.orderTypeList[2]"),
  },
  {
    value: 3,
    label: t("omsOrder.orderTypeList[3]"),
  },
  {
    value: 4,
    label: t("omsOrder.orderTypeList[4]"),
  },
  {
    value: 5,
    label: t("omsOrder.orderTypeList[5]"),
  },
  {
    value: 9,
    label: t("omsOrder.orderTypeList[9]"),
  },
]);
/*销售类型：不需要计算单价和金额的集合*/
const orderTypeNoPrice = [3, 4, 9];

/*结算方式： 1：现金 2：挂账 3：免结算*/
const paymentTypeList = ref([
  {
    value: 1,
    label: t("omsOrder.paymentTypeList[1]"),
  },
  {
    value: 2,
    label: t("omsOrder.paymentTypeList[2]"),
  },
  {
    value: 3,
    label: t("omsOrder.paymentTypeList[3]"),
  },
]);
/*结算方式：不需要计算单价和金额的集合*/
const payTypeNoPrice = [3];

//不计算商品价格 flag
const isOrderFree = computed(() => {
  return orderTypeNoPrice.includes(form.orderType) || payTypeNoPrice.includes(form.paymentType);
})

/*客户合同*/
const contractList = ref([]);

/*销售人员下拉*/
const salePersonList = ref([]);
/*经办人员下拉*/
const operatorUserList = ref([]);

//系统内用户
function queryPersonList() {
  querySalesPersonUser()
    .then((res) => {
      salePersonList.value = res || [];
      operatorUserList.value = res || [];
    })
    .finally(() => {});
}

/*审核人员下拉*/
const approvePersonList = ref([]);
//审核人员列表 来源审核配置
function queryApproveUserList() {
  OrderAPI.queryApproveUserApi().then((res) => {
    approvePersonList.value = res || [];
  })
}

const warehouseList = ref([]);
/** 仓库列表 */
function getStorehouseSelect() {
  OrderAPI.getStorehouseSelect().then((data) => {
    warehouseList.value = data;
  });
}

/*配送方式*/
const deliveryTypeList = ref([]);
function getDeliveryMethodList() {
  let params = { page: 1, limit: 1000, enableStatus: 1 };
  OrderAPI.queryDeliveryMethodList(params).then((res) => {
    deliveryTypeList.value =
      res?.records?.map((item) => {
        return {
          code: item.id,
          name:
            item.deliveryMethodsCategoryVO.deliveryMethodsCategoryName +
            "/" +
            item.methodName,
          methodName: item.methodName,
        };
      }) || [];
  });
}
/*动态获取分类和配送方式*/
const deliveryMethodProps: CascaderProps = {
  lazy: true,
  lazyLoad(node, resolve) {
    const { level } = node;
    if (level == 0) {
      let params = { page: 1, limit: 1000, enableStatus: 1 };
      OrderAPI.queryDeliveryMethodsCategoryList(params).then((res) => {
        const level0Nodes =
          res?.records?.map((item) => ({
            value: item.id,
            label: item.deliveryMethodsCategoryName,
            leaf: false,
          })) || [];
        resolve(level0Nodes);
      });
    } else {
      let params = {
        page: 1,
        limit: 1000,
        deliveryMethodsCategoryId: node?.data?.value,
      };
      OrderAPI.queryDeliveryMethodList(params).then((res) => {
        const level1Nodes =
          res?.records?.map((item) => ({
            value: item.id,
            label: item.methodName,
            leaf: true,
          })) || [];
        resolve(level1Nodes);
      });
    }
  },
};

const customerList = ref([]);
const form = reactive<OrderFrom>({
  contactAreaCode: "+86",
  currencyCode: "CNY",
  currencyName: "人民币",
  orderDetailDTOList: [],
  handlerUserId: userStore?.user?.userId,
  handler: userStore?.user?.nickName,
  isPresale: 0,
  customerAttributes: 2,
  discountType: 1,
  totalAmountCopy: null,
});
/*详情页面跳过来的补发，回显销售类型和单号*/
const oldOrderCodeOptionData = ref([]);
if(route.query?.fromPage == 'orderDetailReissue' && route.query?.orderCode){
  form.orderType = 4;
  form.originalOrderCode = route.query.orderCode;
  oldOrderCodeOptionData.value = [{value: form.originalOrderCode, label: form.originalOrderCode}]
}
const queryParams = reactive<ProductAllPageQuery>({
  page: 1,
  limit: 20,
});

const amountPatternRexp = /^(-?0(?:\.\d{1,2})?|-?[1-9]\d{0,8}(?:\.\d{1,2})?)$/;
const rules = reactive({
  orderTheme: [{required: false, message: t("omsOrder.rules.orderTheme"), trigger: ["blur", "change"]}],
  salesId: [{required: true, message: t("omsOrder.rules.salesId"), trigger: ["blur", "change"],}],
  customerCode: [{required: true, message: t("omsOrder.rules.customerCode"), trigger: ["blur", "change"]}],
  contractId: [{required: false, message: t("omsOrder.rules.contract"), trigger: ["blur", "change"]}],
  orderType: [{required: true, message: t("omsOrder.rules.orderType"), trigger: ["blur", "change"]}],
  exchangeCode: [
    {required: false, message: t("omsOrder.rules.exchangeCode"), trigger: ["blur", "change"],},
    {pattern: /^[a-zA-Z0-9]+$/, message: t("omsOrder.rules.exchangeCodeFormat"), trigger: "blur",},
  ],
  originalOrderCode: [
    {required: true, message: t("omsOrder.rules.originalOrderCode"), trigger: ["blur", "change"],},
    {pattern: /^[a-zA-Z0-9]+$/, message: t("omsOrder.rules.originalOrderCodeFormat"), trigger: "blur",},
  ],
  customerAttributes: [{required: true, message: t("omsOrder.rules.customerAttributes"), trigger: ["blur", "change"]}],
  isPresale: [{required: true, message: t("omsOrder.rules.isPresale"), trigger: ["blur", "change"]}],
  approveUser: [{required: true, message: t("omsOrder.rules.approveUser"), trigger: ["blur", "change"]}],
  paymentType: [{required: true, message: t("omsOrder.rules.paymentType"), trigger: ["blur", "change"]}],
  warehouseId: [{required: true, message: t("omsOrder.rules.warehouseName"), trigger: ["blur", "change"]}],
  deliveryType: [{required: true, message: t("omsOrder.rules.deliveryMethod"), trigger: ["blur", "change"]}],
  totalAmountCopy: [
    {required: true, message: t("omsOrder.rules.totalAmountCopy"), trigger: ["blur", "change"],},
    {pattern: amountPatternRexp, message: t("omsOrder.rules.totalAmountCopyFormat"), trigger: "blur",},
  ],
  discountAmount: [
    {required: true, message: t("omsOrder.rules.discountAmount"), trigger: ["blur", "change"],},
    {pattern: amountPatternRexp, message: t("omsOrder.rules.discountAmountFormat"), trigger: "blur",},
    {required: true, validator: validatorDiscountAmount, trigger: ["blur", "change"],},
  ],
  discountValue: [{required: true, message: t("omsOrder.rules.discountValue"), trigger: ["blur", "change"],},],
  totalDiscountedAmount: [
    {required: true, message: t("omsOrder.rules.totalDiscountedAmount"), trigger: ["blur", "change"],},
    {pattern: amountPatternRexp, message: t("omsOrder.rules.totalDiscountedAmountFormat"), trigger: "blur",},
  ],
  planDeliveryDate: [{required: true, message: t("omsOrder.rules.planDeliveryDate"), trigger: ["blur", "change"],},],
  planDeliveryTime: [{required: false, message: t("omsOrder.rules.planDeliveryTime"), trigger: ["blur", "change"],},],
  contactPerson: [
    {required: false, message: t("omsOrder.rules.contactPerson"), trigger: ["blur", "change"],},
    {pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/, message: t("omsOrder.rules.contactPersonFormat"), trigger: ["blur", "change"],},
  ],
  contactMobile: [
    {required: false, message: t("omsOrder.rules.contactMobile"), trigger: ["blur", "change"],},
    {pattern: /^\d{1,30}$/, message: t("omsOrder.rules.contactMobileFormat"), trigger: ["blur", "change"],},
  ],
  countryId: [{required: false, message: t("omsOrder.rules.countryTip"), trigger: ["blur", "change"],},],
  areaInfo: [{required: false, message: t("omsOrder.rules.areaTip"), trigger: ["blur", "change"],},],
  address: [{required: false, message: t("omsOrder.rules.addressTip"), trigger: "blur",},],
});

// 获取区号
function getAreaList() {
  UserAPI.getAllCountry()
    .then((data) => {
      countryNumCodeList.value = data;
    })
    .finally(() => {});
}

/*附件上传*/
function onChangeMultiple(val) {
  form.orderAttachmentFiles = val ? val : "";
}

/*销售价校验*/
function validatorSalePrice(rule, value, callback) {
  // let index = rule.field?.split('.')[1]
  // if(value && form.orderDetailDTOList[index].min && form.orderDetailDTOList[index].max){
  //     let data =parseFloat(value)
  //     let min =parseFloat(form.orderDetailDTOList[index].min)
  //     let max =parseFloat(form.orderDetailDTOList[index].max)
  //     if (data >  max || data < min) {
  //         callback(new Error(t('omsOrder.message.salePriceTips')));
  //     } else {
  //         callback();
  //     }
  // }else{
  //     callback();
  // }
  callback();
}

/*优惠方式校验*/
function validatorDiscountAmount(rule, value, callback) {
  if (!isEmpty(value) && !isEmpty(form.totalAmountCopy)) {
    if (parseFloat(value) > parseFloat(form.totalAmountCopy)) {
      callback(new Error(t("omsOrder.message.discountAmountTips")));
    } else {
      callback();
    }
  }
}
/*优惠方式切换*/
function changeDiscountType(val) {
  if (val === 2) {
    form.discountValue = null;
  } else if (val === 3) {
    form.discountAmount = null;
  }
}

/*商品总金额change*/
function totalAmountCopyChange() {
  //修改商品总金额时清空优惠金额
  form.discountAmount = null;
}

/*销售人员change*/
function saleUserChange(val) {
  if (val && salePersonList.value?.find((item) => item.userId == val)) {
    form.salesName = salePersonList.value?.find(
      (item) => item.userId === val
    )?.nickName;
  } else {
    form.salesName = null;
  }
}

/*审核人change*/
function approveUserChange(val) {
  if (val && approvePersonList.value?.find((item) => item.userId == val)) {
    form.approveUserName = approvePersonList.value?.find(
      (item) => item.userId === val
    )?.nickName;
  } else {
    form.approveUserName = null;
  }
}
/*经办人change*/
function handlerUserChange(val) {
  if (val && operatorUserList.value?.find((item) => item.userId == val)) {
    form.handler = operatorUserList.value?.find(
      (item) => item.userId === val
    )?.nickName;
  } else {
    form.handler = null;
  }
}
/*配送方式change*/
function deliveryMethodChange(val) {
  if (val && deliveryTypeList.value?.find((item) => item.code == val)) {
    form.deliveryName = deliveryTypeList.value?.find(
      (item) => item.code === val
    )?.name;
  } else {
    form.deliveryName = null;
  }
}
/*仓库change*/
function warehouseChange(val) {
  if (val && warehouseList.value?.find((item) => item.warehouseId == val)) {
    form.warehouseName = warehouseList.value?.find(
      (item) => item.warehouseId == val
    )?.warehouseName;
    form.warehouseCode = warehouseList.value?.find(
      (item) => item.warehouseId == val
    )?.warehouseCode;
  } else {
    form.warehouseName = null;
    form.warehouseCode = null;
  }
}
/*客户合同change*/
function contractChange(val) {
  if (val && contractList.value?.find((item) => item.contractId == val)) {
    let selectContract = contractList.value?.find(
      (item) => item.contractId == val
    );
    form.contractCode = selectContract?.contractCode;
    form.contractName = selectContract?.contractName;
    form.contractType = selectContract?.contractType;
  } else {
    form.contractCode = null;
    form.contractName = null;
    form.contractType = null;
  }
}

//商品的数量和销售价change
function saleChange(index, val) {
  if (
    form.orderDetailDTOList[index].saleCount !== undefined &&
    form.orderDetailDTOList[index].salePrice !== undefined
  ) {
    if (val == 1) {
      let saleMoney = (
        form.orderDetailDTOList[index].saleCount *
        form.orderDetailDTOList[index].salePrice
      ).toFixed(2);
      form.orderDetailDTOList[index].saleMoney = saleMoney;
    }
    let totalAmount = "0";
    form.orderDetailDTOList.forEach((item) => {
      let totalMoney1 = item.saleMoney ? parseFloat(item.saleMoney) : 0;
      totalAmount = (parseFloat(totalAmount) + totalMoney1).toFixed(2);
    });
    form.totalAmount = totalAmount;
  }
}

//结算方式change
function paymentTypeChange(val: number) {
  /* 销售类型 3: 业务招待 4: 补发  9: 参展 || 结算方式 3：免结算  ===》商品单价和金额为0*/
  if(isOrderFree.value){//免结算 商品单价金额置为0
    calculateProductFree()
  }else{//重新按照商品单价和数量计算金额
    calculateProductByPrice()
  }
}

/** 查询客户列表 */
function getCustomerSelectList() {
  return new Promise((resolve, reject) => {
    let params = {
      enableStatus: 1, //(可选0:停用 1:正常)
    };
    CustomerApi.getCustomerSelectList(params)
      .then((data) => {
        customerList.value = data;
        resolve();
      })
      .catch((error) => {
        reject(error);
      })
      .finally(() => {
        loading.value = false;
      });
  });
}

function onSubmit(data) {
  data.forEach((item) => {
    item.imagesUrls = item.mainImageUrl;
    item.productSpecs = item.productSpec;
    item.floatRange = item.saleAmountRadio;
    item.oldSaleAmount = item.saleAmount;
    item.currencyCode = "CNY";
    item.currencyName = "人民币";
    item.remark = "";
    if (item.oldSaleAmount !== undefined && item.floatRange !== undefined) {
      item.max = parseFloat(
        item.oldSaleAmount * (1 + parseFloat(item.floatRange) / 100)
      ).toFixed(2);
      item.min = parseFloat(
        item.oldSaleAmount * (1 - parseFloat(item.floatRange) / 100)
      ).toFixed(2);
    }
    //免结算 商品销售价置为0
    if(isOrderFree.value){
      item.salePrice = 0;
    }else{
      item.salePrice = item.saleAmount;
    }
  });
  let arr = data.concat(form.orderDetailDTOList);
  let uniqueArr = [
    ...new Map(arr.map((item) => [item.productCode, item])).values(),
  ];
  form.orderDetailDTOList = uniqueArr;
}

/** 假删除*/
function handleDelete(index?: number) {
  let saleMoney = form.orderDetailDTOList[index].saleMoney
    ? parseFloat(form.orderDetailDTOList[index].saleMoney)
    : 0;
  let totalAmount = form.totalAmount ? parseFloat(form.totalAmount) : 0;
  form.totalAmount = (totalAmount - saleMoney).toFixed(2);
  form.orderDetailDTOList.splice(index, 1);
  ElMessage.success(t("omsOrder.message.deleteSucess"));
}

async function handleBack() {
  router.push({ path: "/oms/order/orderManage" })
}

/** 添加/编辑订单 */
function handleSubmit(val) {
  if (form.orderDetailDTOList && form.orderDetailDTOList.length == 0) {
    return ElMessage.error(t("omsOrder.message.addOrEditOrderTips"));
  }
  fromRef.value.validate((valid) => {
    if (!valid) return;
    submitLoading.value = true;
    let customer = customerList.value.filter(function (item) {
      return form.customerCode == item.customerCode;
    });
    let orderDetailDTOList = [];
    if (form.orderDetailDTOList && form.orderDetailDTOList.length > 0) {
      form.orderDetailDTOList.forEach((item) => {
        let obj = {
          ...item,
          // imagesUrls: item.imagesUrls,
          // productCode: item.productCode,
          // productName: item.productName,
          // productSpecs: item.productSpecs,
          // currencyCode: item.currencyCode,
          // currencyName: item.currencyName,
          productQty: item.saleCount,
          saleAmount: item.salePrice,
          // oldSaleAmount: item.oldSaleAmount,
          saleAmountRadio: item.floatRange,
          totalSaleAmount: item.saleMoney,
          // firstCategoryId: item.firstCategoryId,
          // secondCategoryId: item.secondCategoryId,
          // thirdCategoryId: item.thirdCategoryId,
          // firstCategoryName: item.firstCategoryName,
          // secondCategoryName: item.secondCategoryName,
          // thirdCategoryName: item.thirdCategoryName,
          // productUnitId: item.productUnitId,
          // productUnitName: item.productUnitName,
        };
        if (type !== "add") {
          obj.orderId = form.id;
          obj.orderCode = form.orderCode;
        }
        orderDetailDTOList.push(obj);
      });
    }
    let params = {
      ...form,
      // orderCode: form.orderCode,
      // customerCode: form.customerCode,
      customerName: customer[0]?.customerName,
      currencyCode: type == "add" ? orderDetailDTOList[0].currencyCode : form.currencyCode,
      currencyName: type == "add" ? orderDetailDTOList[0].currencyName : form.currencyName,
      // contactPerson: form.contactPerson,
      // contactAreaCode: form.contactAreaCode,
      // contactMobile: form.contactMobile,
      // countryId: form.countryId,
      // countryName: form.countryName,
      // provinceId: form.provinceId,
      // provinceName: form.provinceName,
      // cityId: form.cityId,
      // cityName: form.cityName,
      // districtId: form.districtId,
      // districtName: form.districtName,
      // address: form.address,
      expectedReceivedTimeBase: form?.planDeliveryDate?.length ? convertToTimestamp(form.planDeliveryDate + " 00:00:00") : "",
      expectedReceivedTimeStar: form?.planDeliveryTime?.length ? convertToTimestamp(form.planDeliveryDate + " " + form.planDeliveryTime[0]) : "",
      expectedReceivedTimeEnd: form?.planDeliveryTime?.length ? convertToTimestamp(form.planDeliveryDate + " " + form.planDeliveryTime[1]) : "",
      orderSource: form.orderSource !== undefined && form.orderSource !== null ? form.orderSource : 1,
      // remark: form.remark,
      orderAttachmentFiles: JSON.stringify(form.orderAttachmentFiles),
      orderDetailDTOList: orderDetailDTOList,
      approveFlag: val != 0,
      totalAmount: form.totalAmountCopy,
    };
    if (form.discountType == 2) {
      params.discountValue = form.discountAmount;
    } else if (form.discountType == 3) {
      params.discountAmount = null;
    }
    if (type == "add") {
      OrderAPI.addOrder(params)
        .then((data) => {
          if (val == 0) {
            ElMessage.success(t("omsOrder.message.saveDraftOrderSucess"));
            localStorage.setItem("tabs", 0);
          } else {
            ElMessage.success(t("omsOrder.message.submitOrderSucess"));
            localStorage.setItem("tabs", 1);
          }
          handleBack();
        })
        .finally(() => {
          submitLoading.value = false;
        });
    } else {
      params.id = form.id;
      params.updateFlag = type == "edit" || (type == "check" && deliveryScheduleDelete == "no");
      OrderAPI.editOrder(params)
        .then((data) => {
          if (val == 0) {
            ElMessage.success(t("omsOrder.message.saveDraftOrderSucess"));
            localStorage.setItem("tabs", 0);
          } else if (val == 1) {
            ElMessage.success(t("omsOrder.message.submitOrderSucess"));
            localStorage.setItem("tabs", 1);
          } else {
            ElMessage.success(t("omsOrder.message.saveCheckSucess"));
            localStorage.setItem("tabs", 2);
          }
          handleBack();
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}
const isFirstLoad = ref(false);

/** 查询订单详情 */
function queryDetailForEdit() {
  loading.value = true;
  let params = {
    id: id,
    orderCode: orderCode,
  };
  OrderAPI.queryDetailForEdit(params)
    .then((data) => {
      Object.assign(form, data);
      if (form.discountType == 2) {
        form.discountAmount = form.discountValue;
        form.discountValue = null;
      }
      form.expectedReceivedTimeBase = form.expectedReceivedTimeBase ? parseDateTime(form.expectedReceivedTimeBase, "dateTime").split(" ")[0] : "";
      form.expectedReceivedTimeStar = form.expectedReceivedTimeStar ? parseDateTime(form.expectedReceivedTimeStar, "dateTime").split(" ")[1] : "";
      form.expectedReceivedTimeEnd = form.expectedReceivedTimeEnd ? parseDateTime(form.expectedReceivedTimeEnd, "dateTime").split(" ")[1] : "";
      form.planDeliveryDate = form.expectedReceivedTimeBase;
      form.planDeliveryTime = [form.expectedReceivedTimeStar, form.expectedReceivedTimeEnd,];
      let orderDetailList = [];
      if (form.orderDetailList && form.orderDetailList.length > 0) {
        form.orderDetailList.forEach((item) => {
          let obj = {
            ...item,
            // orderId: item.orderId,
            // orderCode: item.orderCode,
            mainImageUrl: item.imagesUrls,
            // imagesUrls: item.imagesUrls,
            // productCode: item.productCode,
            // productName: item.productName,
            // productSpecs: item.productSpecs,
            // currencyCode: item.currencyCode,
            // currencyName: item.currencyName,
            saleCount: item.productQty,
            salePrice: item.saleAmount,
            // oldSaleAmount: item.oldSaleAmount,
            floatRange: item.saleAmountRadio,
            saleMoney: item.totalSaleAmount,
            // firstCategoryId: item.firstCategoryId,
            // secondCategoryId: item.secondCategoryId,
            // thirdCategoryId: item.thirdCategoryId,
            // firstCategoryName: item.firstCategoryName,
            // secondCategoryName: item.secondCategoryName,
            // thirdCategoryName: item.thirdCategoryName,
            // productUnitId: item.productUnitId,
            // productUnitName: item.productUnitName,
          };
          if (obj.oldSaleAmount && obj.floatRange) {
            obj.max = parseFloat(
              obj.oldSaleAmount * (1 + parseFloat(obj.floatRange) / 100)
            ).toFixed(2);
            obj.min = parseFloat(
              obj.oldSaleAmount * (1 - parseFloat(obj.floatRange) / 100)
            ).toFixed(2);
          }
          orderDetailList.push(obj);
        });
      }
      form.orderDetailDTOList = orderDetailList;
      if (
        form.orderAttachmentFiles &&
        typeof form.orderAttachmentFiles === "string"
      ) {
        form.orderAttachmentFiles = JSON.parse(form.orderAttachmentFiles);
      }
      form.areaInfo = [form.countryId, form.provinceId, form.cityId, form.districtId,];
      if (form.countryId) {
        countryChange(form.countryId, 2);
      }
      if (form.provinceId) {
        handleProvinceChange(form.provinceId, 2);
      }
      if (form.cityId) {
        handleCityChange(form.cityId, 2);
      }
      form.totalAmount = data?.originalTotalAmount;
      form.totalAmountCopy = data?.totalAmount;
      isFirstLoad.value = true;

      if (form?.customerCode) {
        let customerId = customerList.value?.find((item) => item.customerCode === form.customerCode)?.id;
        queryContractData(customerId);
      }

      if(form?.orderType == 4){
        oldOrderCodeOptionData.value = [{value: form.originalOrderCode, label: form.originalOrderCode}]
      }
      /*处理旧数据 审核人未在审核人列表 清空审核人数据*/
      if(approvePersonList.value?.length && !approvePersonList.value?.some(item => item.userId == form.approveUser)){
        form.approveUser = undefined;
        form.approveUserName = '';
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 添加商品 */
async function addProduct() {
  dialog.title = t("omsOrder.title.addProduct");
  let data = {};
  addProductRef.value.setFormData({ queryParams: data });
  addProductRef.value.queryManagerCategoryList();
  dialog.visible = true;
}
/** 获取国家列表 */
function queryAllCountry() {
  const queryParams = {
    pid: "0",
  };
  CommonAPI.getAreaList(queryParams)
    .then((data: any) => {
      countryList.value = data;
    })
    .finally(() => {});
}

function orderTypeChange(val: any) {
  /* 销售类型 3: 业务招待 4: 补发  9: 参展 || 结算方式 3：免结算  ===》商品单价和金额为0*/
  if(isOrderFree.value){
    if(form.orderDetailDTOList?.length){
      calculateProductFree()
    }
    /*将客户属性置为其他 ，并清空客户、合同数据*/
    if([3,9].includes(val)){
      form.customerAttributes = 3;
      clearCustomerContractData()
    }
  }else if(!isEmpty(val) && form.orderDetailDTOList?.length){
    calculateProductByPrice()
  }

  //审核人员默认回显当前登陆用户 1:普通销售 2:提货卡兑换 5：地头售卖
  if( [1,2,5,].includes(val)){
    approveUserSetDefaultValue('setDefault')
  }else{
    approveUserSetDefaultValue('emptyValue')
  }
}

/*回显审核人员逻辑 处理*/
function approveUserSetDefaultValue(type: string) {
  if(type == 'setDefault' && approvePersonList.value?.some(item => item.userId == userStore?.user?.userId)){
    form.approveUser = userStore?.user?.userId;
    form.approveUserName = userStore?.user?.nickName;
  }
  if(type == 'emptyValue'){
    form.approveUser = '';
    form.approveUserName = '';
  }
}

/*商品的销售价和金额,总金额 免费将销售价和金额总金额置为0*/
function calculateProductFree() {
  form.orderDetailDTOList?.forEach((item:any) => {
    item.salePrice = 0;
    item.saleMoney = 0;
  });
  form.totalAmount = 0;
}
/*商品的销售价和金额、总金额不免费计算*/
function calculateProductByPrice() {
  let totalAmount = 0;
  form.orderDetailDTOList?.forEach((item:any) => {
    item.salePrice = item.oldSaleAmount;
    item.saleMoney = 0;
    if(!isEmpty(item.salePrice) && !isEmpty(item.saleCount)){
      item.saleMoney = (item.salePrice * item.saleCount).toFixed(2);
    }
    totalAmount = (Number(totalAmount) + Number(item.saleMoney)).toFixed(2);
  });
  form.totalAmount = totalAmount;
}


const originalOrderCodeChange = (queryCode: string) => {
  if(!queryCode){return false}
  useDebounce(() => {
    oldOrderCodeOptionData.value = [];
    OrderAPI.queryOldOrderCodeApi( queryCode).then((data) => {
      oldOrderCodeOptionData.value = data?.map(item => {
       return {
         value: item,
         label: item,
       }
      }) || []
    }).finally(() => {})
  },1000)
}

function countryChange(val: any, num: number) {
  if (num !== 2) {
    form.provinceId = "";
    provinceList.value = [];
    form.cityId = "";
    cityList.value = [];
    form.districtId = "";
    districtList.value = [];
    showProvinceInput.value = false;
    showCityInput.value = false;
    showDistrictInput.value = false;
    form.areaInfo = [];
    form.provinceName = "";
    form.cityName = "";
    form.districtName = "";
      let data: any = countryList.value.find(
        (item: any) => item.id === form.countryId
      );
      form.countryName = data?.shortName ? data.shortName : '';
      let params = {
        pid: val,
      };
      if(isEmpty(val)) { return }
      CommonAPI.getAreaList(params)
        .then((data: any) => {
          if (data.length > 0) {
            provinceList.value = data;
            showProvinceInput.value = true;
            // form.areaInfo[0] = form.countryId;
          } else {
            showProvinceInput.value = false;
            form.areaInfo[0] = form.countryId;
            fromRef.value.clearValidate("areaInfo");
          }
        })
        .finally(() => {});
  } else {
    let params = {
      pid: val,
    };
    if(isEmpty(val)) { return }
    CommonAPI.getAreaList(params)
      .then((data: any) => {
        provinceList.value = data;
        showProvinceInput.value = true;
        // form.areaInfo[0] = form.countryId;
      })
      .finally(() => {});
  }
}

function handleProvinceChange(val: any, num: number) {
  if (num !== 2) {
    form.cityId = "";
    cityList.value = [];
    form.districtId = "";
    districtList.value = [];
    showCityInput.value = false;
    showDistrictInput.value = false;
    form.areaInfo = [];
    let data: any = provinceList.value.find(
      (item: any) => item.id === form.provinceId
    );
    form.provinceName = data?.shortName ? data.shortName : "";
    let params = {
      pid: val,
    };
    if(isEmpty(val)) { return }
    CommonAPI.getAreaList(params)
      .then((data: any) => {
        if (data.length > 0) {
          cityList.value = data;
          showCityInput.value = true;
          /* form.areaInfo[0] =  form.countryId;
                        form.areaInfo[1] = form.provinceId;
                        form.areaInfo[2] = form.cityId;*/
        } else {
          showCityInput.value = false;
          form.areaInfo[0] = form.countryId;
          form.areaInfo[1] = form.provinceId;
          fromRef.value.clearValidate("areaInfo");
        }
      })
      .finally(() => {});
  } else {
    let params = {
      pid: val,
    };
    if(isEmpty(val)) { return }
    CommonAPI.getAreaList(params)
      .then((data: any) => {
        if (data.length > 0) {
          cityList.value = data;
          showCityInput.value = true;
          form.areaInfo[0] = form.countryId;
          form.areaInfo[1] = form.provinceId;
          form.areaInfo[2] = form.cityId;
        } else {
          showCityInput.value = false;
        }
      })
      .finally(() => {});
  }
}

function handleCityChange(val: any, num: number) {
  if (num !== 2) {
    form.districtId = "";
    districtList.value = [];
    showDistrictInput.value = false;
    form.areaInfo = [];
    let data: any = cityList.value.find((item: any) => item.id === form.cityId);
    form.cityName = data?.shortName ? data.shortName : "";
    let params = {
      pid: val,
    };
    if(isEmpty(val)) { return }
    CommonAPI.getAreaList(params)
      .then((data: any) => {
        if (data.length > 0) {
          showDistrictInput.value = true;
          /* form.areaInfo[0] =  form.countryId;
                        form.areaInfo[1] = form.provinceId;
                        form.areaInfo[2] = form.cityId;
                        form.areaInfo[3] = form.districtId;*/
          districtList.value = data;
        } else {
          showDistrictInput.value = false;
          form.areaInfo[0] = form.countryId;
          form.areaInfo[1] = form.provinceId;
          form.areaInfo[2] = form.cityId;
          form.areaInfo[3] = form.districtId;
          fromRef.value.clearValidate("areaInfo");
        }
      })
      .finally(() => {});
  } else {
    showDistrictInput.value = false;
    if(isEmpty(val)) { return }
    let params = {
      pid: val,
    };
    CommonAPI.getAreaList(params)
      .then((data: any) => {
        if (data.length > 0) {
          showDistrictInput.value = true;
          form.areaInfo[3] = form.districtId;
          districtList.value = data;
          handleDistrictChange();
        } else {
          showDistrictInput.value = false;
        }
      })
      .finally(() => {});
  }
}

function handleDistrictChange() {
  let data: any = districtList.value.find(
    (item: any) => item.id === form.districtId
  );
  form.districtName = data && data.shortName ? data.shortName : "";
  if (form.districtId) {
    form.areaInfo[3] = form.districtId;
  } else {
    form.areaInfo = [];
  }
}

function customerAttributesChange (val: number) {
  //散客和其他清除客户、合同
  if([2,3].includes(val)){
    clearCustomerContractData()
  }
}

function clearCustomerContractData() {
  form.customerId = null;
  form.customerName = null;
  form.customerCode = null;
  form.contractCode = null;
  form.contractName = null;
  form.contractType = null;
  form.contractId = null;
}


function customerChange() {
  let customer = customerList.value.filter(
    (out) => form.customerCode == out.customerCode
  );
  //客户切换，清空合同下拉数据
  emptyContract()

  if (customer && customer.length > 0) {
    form.customerId = customer[0].id;
    form.contactPerson = customer[0].receiverName;
    form.contactAreaCode = customer[0].receiverCountryAreaCode;
    form.contactMobile = customer[0].receiverMobile;
    form.countryId = customer[0].receiverCountryCode;
    form.countryName = customer[0].receiverCountryName;
    showProvinceInput.value = customer[0].receiverProvinceCode ? true : false;
    form.provinceId = customer[0].receiverProvinceCode;
    form.provinceName = customer[0].receiverProvinceName;
    showCityInput.value = customer[0].receiverCityCode ? true : false;
    form.cityId = customer[0].receiverCityCode;
    form.cityName = customer[0].receiverCityName;
    form.districtId = customer[0].receiverDistrictCode;
    showDistrictInput.value = customer[0].receiverDistrictCode ? true : false;
    form.districtId = customer[0].receiverDistrictCode;
    form.districtName = customer[0].receiverDistrictName;
    form.address = customer[0].receiverAddress;
    form.areaInfo = [
      form.countryId,
      form.provinceId,
      form.cityId,
      form.districtId,
    ];
    if (form.countryId) {
      countryChange(form.countryId, 2);
    }
    if (form.provinceId) {
      handleProvinceChange(form.provinceId, 2);
    }
    if (form.cityId) {
      handleCityChange(form.cityId, 2);
    }
    /*查询客户关联合同*/
    queryContractData(customer[0].id);
  }
}

function emptyContract() {
  contractList.value = [];
  form.contractCode = null;
  form.contractName = null;
  form.contractType = null;
  form.contractId = null;
}

function queryContractData(id: string) {
  OrderAPI.queryCustomerContractList({
    customerId: id,
  })
    .then((res: any) => {
      contractList.value = res?.map((item) => item) || [];
    })
    .finally(() => {});
}

/*商品总金额监听*/
watch(() => form.totalAmount, (newVal, oldVal) => {
    if(!isFirstLoad.value){
      form.totalAmountCopy = newVal;
    }else{
      isFirstLoad.value = false
    }
  }, { immediate: true });

const totalDiscountedAmountComputed = computed(() => {
  if (form.discountType === 3 && !isEmpty(form.totalAmountCopy) && !isEmpty(form.discountValue)) {
    //折扣
    return (form.totalAmountCopy * (form.discountValue / 100)).toFixed(2);
  } else if (form.discountType === 2 && !isEmpty(form.totalAmountCopy) && !isEmpty(form.discountAmount)) {
    return (form.totalAmountCopy - form.discountAmount).toFixed(2);
  } else {
    //无优惠
    return isEmpty(form.totalAmountCopy) ? null : parseFloat(form.totalAmountCopy);
  }
});
watch(() => totalDiscountedAmountComputed.value, (newVal, oldVal) => {
    form.totalDiscountedAmount = isEmpty(newVal) ? null : newVal;
  }, { immediate: true });

onMounted(async () => {
  await getCustomerSelectList();
  getAreaList();
  queryAllCountry();
  queryPersonList();
  //查询审核人下拉列表
  await queryApproveUserList();
  getStorehouseSelect();
  getDeliveryMethodList();
  if (type == "edit" || type == "check") {
    showDisabled.value = true;
    confirmDisabled.value = true;
    queryDetailForEdit();
  }
});
</script>
<style scoped lang="scss">
.addOrder {
  background: #ffffff;
  border-radius: 4px;
  .page-content {
    .button-add {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary);
    }
    .table-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 50px;
      background: #f4f6fa;
      box-shadow:
        inset 1px 1px 0px 0px #e5e7f3,
        inset -1px -1px 0px 0px #e5e7f3;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585f;
      font-style: normal;
      padding: 15px 12px;
    }
  }
}
</style>
