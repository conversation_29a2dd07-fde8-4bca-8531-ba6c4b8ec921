<template>
  <div class="app-container">
    <div class="outboundNoticeDetail" v-loading="loading">
      <div class="page-content">
        <el-form :model="form" label-width="110px" label-position="right">
          <div class="flex-center-but">
            <div class="title-lable">
              <div class="title-line"></div>
              <div class="title-content">
                {{ $t("quickOutbound.label.documentInformation") }}
              </div>
            </div>
            <div v-if="firstTableShow" @click="closeFirstTable" style="font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 14px;color: #762ADB;line-height: 22px;text-align: left;font-style: normal;cursor: default">收起</div>
            <div v-if="!firstTableShow" @click="openFirstTable" style="font-family: PingFangSC, PingFang SC;font-weight: 500;font-size: 14px;color: #762ADB;line-height: 22px;text-align: left;font-style: normal;cursor: default">展开</div>
          </div>
          <div class="grad-row">
            <table v-if="firstTableShow" style="border: 1px solid #E5E7F3  !important;border-collapse: collapse;width: 100%;table-layout: fixed;-webkit-print-color-adjust: exact;">
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.outboundNoticeCode')}}</td>
                <td class="td-value">{{ form.outboundNoticeCode ? form.outboundNoticeCode : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.outboundType')}}</td>
                <td class="td-value" v-if="form.outboundType == 1">{{ $t("quickOutbound.outboundTypeList.procurementOutbound") }}</td>
                <td class="td-value" v-else-if="form.outboundType == 2">{{ $t("quickOutbound.outboundTypeList.pickupCardRedemption") }}</td>
                <td class="td-value" v-else-if="form.outboundType == 3">{{ $t("quickOutbound.outboundTypeList.businessReception") }}</td>
                <td class="td-value" v-else-if="form.outboundType == 4">{{ $t("quickOutbound.outboundTypeList.reissue") }}</td>
                <td class="td-value" v-else-if="form.outboundType == 5">{{ $t("quickOutbound.outboundTypeList.sellAtTheFieldEdge") }}</td>
                <td class="td-value" v-else-if="form.outboundType == 6">{{ $t("quickOutbound.outboundTypeList.returnOutbound") }}</td>
                <td class="td-value" v-else-if="form.outboundType == 7">{{ $t("quickOutbound.outboundTypeList.allotOutbound") }}</td>
                <td class="td-value" v-else-if="form.outboundType == 8">{{ $t("quickOutbound.outboundTypeList.directOutbound") }}</td>
                <td class="td-value" v-else-if="form.outboundType == 9">{{ $t("quickOutbound.outboundTypeList.participateInTheExhibition") }}</td>
                <td class="td-value" v-else>-</td>
                <td class="td-label">{{$t('quickOutbound.label.themeDescription')}}</td>
                <td class="td-value">{{ form.orderTheme ? form.orderTheme : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.sourceOrderCode')}}</td>
                <td class="td-value">{{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}</td>
              </tr>
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.salesperson')}}</td>
                <td class="td-value">{{ form.purchaseSalesPerson ? form.purchaseSalesPerson : "-" }}</td>
                <td class="td-label">{{ form.outboundType == 2 ? '提货卡号' : $t('quickOutbound.label.originalOrderNumber')}}</td>
                <td class="td-value">{{ form.sourceCode ? form.sourceCode : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.createUserName')}}</td>
                <td class="td-value">{{ form.createUserName ? form.createUserName : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.createTime')}}</td>
                <td class="td-value">{{ form.createTime ? parseDateTime(form.createTime, "dateTime") : "-" }}</td>
              </tr>
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.customerName')}}</td>
                <td class="td-value">{{ form.customerName ? form.customerName : "-" }}</td>
                <td class="td-label">{{ $t('quickOutbound.label.customerAddress')}}</td>
                <td class="td-value">
                  <span class="encryptBox">
                    <span v-if="form.addressShow">
                      {{ form.addressFormat }}
                      <el-icon
                        v-if="form.fullAddress"
                        @click="form.addressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.addressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.fullAddress ? form.fullAddress : "-" }}
                    </span>
                  </span>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.customerPerson')}}</td>
                <td class="td-value">
                  {{ form.nameShow ? (form.contactPerson ? form.contactPerson : '-') : encryptName(form.contactPerson) }}
                  <el-icon
                    v-if="form.contactPerson"
                    @click="form.contactPerson ? getRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.nameShow ? '' : 'View'" />
                  </el-icon>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.customerMobile')}}</td>
                <td class="td-value">
                  <span class="encryptBox">
                    <span v-if="form.customerMobile">
                      {{ form.customerAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.customerMobile && form.customerMobile.length <= 4
                      "
                    >
                      {{ form.customerMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.customerMobile && form.customerMobile.length > 4
                      "
                    >
                      {{ form.customerMobile }}
                      <el-icon
                        v-if="form.customerMobile"
                        @click="form.mobilePhoneShow ? getRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </td>
              </tr>
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.supplierName')}}</td>
                <td class="td-value">{{ form.supplierName ? form.supplierName : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.supplierAddress')}}</td>
                <td class="td-value">
                  <span class="encryptBox">
                    <span v-if="form.supplierAddressShow">
                      {{ form.supplierAddressFormat }}
                      <el-icon
                        v-if="form.supplierFullAddress"
                        @click="form.supplierAddressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierAddressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.supplierFullAddress ? form.supplierFullAddress : "-" }}
                    </span>
                  </span>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.supplierPerson')}}</td>
                <td class="td-value">
                  {{ form.supplierNameShow ? (form.supplierContactPerson ? form.supplierContactPerson : '-') : encryptName(form.supplierContactPerson) }}
                  <el-icon
                    v-if="form.supplierContactPerson"
                    @click="form.supplierContactPerson ? getSupplierRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.supplierNameShow ? '' : 'View'" />
                  </el-icon>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.supplierMobile')}}</td>
                <td class="td-value">
                  <span class="encryptBox">
                    <span v-if="form.supplierContactMobile">
                      {{ form.supplierContactAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.supplierContactMobile && form.supplierContactMobile.length <= 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.supplierContactMobile && form.supplierContactMobile.length > 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                      <el-icon
                        v-if="form.supplierContactMobile"
                        @click="form.supplierMobilePhoneShow ? getSupplierRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierMobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </td>
              </tr>
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.contractName')}}</td>
                <td class="td-value">
                  <div style="color: #762ADB;" v-if="form.contractName">{{form.contractName}}</div>
                  <div v-else>-</div>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.contractCode')}}</td>
                <td class="td-value">
                  <div style="color: #762ADB;" v-if="form.contractCode">{{form.contractCode}}</div>
                  <div v-else>-</div>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.contractClassification')}}</td>
                <td class="td-value">
                  <div v-if="form.contractType == 1">销售合同</div>
                  <div v-else-if="form.contractType == 2">采购合同</div>
                  <div v-else>-</div>
                </td>
                <td class="td-label">{{$t('quickOutbound.label.settlementMethod')}}</td>
                <td class="td-value">
                  {{ form.paymentType == 1 ? $t('quickOutbound.label.presentSettlement') : form.paymentType == 2 ? $t('quickOutbound.label.accountHanging') : form.paymentType == 3 ? $t('quickOutbound.label.noSettlementRequired') : "-" }}
                </td>
              </tr>
              <tr>
                <td class="td-label">{{$t('quickOutbound.label.plannedReceivedTime')}}</td>
                <td class="td-value">{{ form.plannedReceivedTime ? parseDateTime(form.plannedReceivedTime,"dateTime") : '-' }}</td>
                <td class="td-label">{{$t('quickOutbound.label.plannedDeliveryTime')}}</td>
                <td class="td-value">{{form.plannedDeliveryTime ? parseDateTime(form.plannedDeliveryTime,"dateTime") : '-'}}</td>
                <td class="td-label">{{$t('quickOutbound.label.plannedDistributionMethod')}}</td>
                <td class="td-value">{{ form.deliveryName ? form.deliveryName : "-" }}</td>
                <td class="td-label">{{$t('quickOutbound.label.sourceOrderRemark')}}</td>
                <td class="td-value">{{form.remark ? form.remark : "-"}}</td>
              </tr>
            </table>
          </div>
          <!--<el-row v-if="firstTableShow">
            &lt;!&ndash; 出库通知单号 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.outboundNoticeCode')">
                {{ form.outboundNoticeCode ? form.outboundNoticeCode : "-" }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              &lt;!&ndash; 出库类型:1:销售出库、2:提货卡兑换、3：业务招待、4：补发、5：地头售卖、6:采购退货、7：调拨出库 &ndash;&gt;
              <el-form-item :label="$t('quickOutbound.label.outboundType')">
                <span v-if="form.outboundType == 1">{{ $t("quickOutbound.outboundTypeList.procurementOutbound") }}</span>
                <span v-else-if="form.outboundType == 2">{{ $t("quickOutbound.outboundTypeList.pickupCardRedemption") }}</span>
                <span v-else-if="form.outboundType == 3">{{ $t("quickOutbound.outboundTypeList.businessReception") }}</span>
                <span v-else-if="form.outboundType == 4">{{ $t("quickOutbound.outboundTypeList.reissue") }}</span>
                <span v-else-if="form.outboundType == 5">{{ $t("quickOutbound.outboundTypeList.sellAtTheFieldEdge") }}</span>
                <span v-else-if="form.outboundType == 6">{{ $t("quickOutbound.outboundTypeList.returnOutbound") }}</span>
                <span v-else-if="form.outboundType == 7">{{ $t("quickOutbound.outboundTypeList.allotOutbound") }}</span>
                <span v-else-if="form.outboundType == 8">{{ $t("quickOutbound.outboundTypeList.directOutbound") }}</span>
                <span v-else-if="form.outboundType == 9">{{ $t("quickOutbound.outboundTypeList.participateInTheExhibition") }}</span>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 主题描述 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.themeDescription')">
                <div style="word-break: break-all;">
                  {{ form.orderTheme ? form.orderTheme : "-" }}
                </div>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 来源单号 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.sourceOrderCode')">
                {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
              </el-form-item>
            </el-col>
            &lt;!&ndash; 业务员 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.salesperson')">
                {{ form.purchaseSalesPerson ? form.purchaseSalesPerson : "-" }}
              </el-form-item>
            </el-col>
            &lt;!&ndash; 原单号 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="form.outboundType == 2 ? '提货卡号' : $t('quickOutbound.label.originalOrderNumber')">
                {{ form.sourceCode ? form.sourceCode : "-" }}
              </el-form-item>
            </el-col>
            &lt;!&ndash; 申请人 &ndash;&gt;
            <el-col :span="6">
              <el-form-item
                :label="$t('quickOutbound.label.createUserName')"
              >
                {{ form.createUserName ? form.createUserName : "-" }}
              </el-form-item>
            </el-col>
            &lt;!&ndash; 申请时间 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.createTime')">
                  <span v-if="form.createTime">
                    {{ parseTime(form.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
                  </span>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 客户 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.customerName')">
                {{ form.customerName ? form.customerName : "-" }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              &lt;!&ndash; 客户地址 &ndash;&gt;
              <el-form-item :label="$t('quickOutbound.label.customerAddress')">
                  <span class="encryptBox">
                    <span v-if="form.addressShow">
                      {{ form.addressFormat }}
                      <el-icon
                        v-if="form.fullAddress"
                        @click="form.addressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.addressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.fullAddress ? form.fullAddress : "-" }}
                    </span>
                  </span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              &lt;!&ndash; 客户联系人 &ndash;&gt;
              <el-form-item :label="$t('quickOutbound.label.customerPerson')">
                {{ form.nameShow ? (form.contactPerson ? form.contactPerson : '-') : encryptName(form.contactPerson) }}
                <el-icon
                  v-if="form.contactPerson"
                  @click="form.contactPerson ? getRealName() : ''"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                >
                  <component :is="form.nameShow ? '' : 'View'" />
                </el-icon>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              &lt;!&ndash; 客户联系电话 &ndash;&gt;
              <el-form-item :label="$t('quickOutbound.label.customerMobile')">
                  <span class="encryptBox">
                    <span v-if="form.customerMobile">
                      {{ form.customerAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.customerMobile && form.customerMobile.length <= 4
                      "
                    >
                      {{ form.customerMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.customerMobile && form.customerMobile.length > 4
                      "
                    >
                      {{ form.customerMobile }}
                      <el-icon
                        v-if="form.customerMobile"
                        @click="form.mobilePhoneShow ? getRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 供应商 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.supplierName')" >
                {{ form.supplierName ? form.supplierName : "-" }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              &lt;!&ndash; 供应商地址 &ndash;&gt;
              <el-form-item :label="$t('quickOutbound.label.supplierAddress')">
                  <span class="encryptBox">
                    <span v-if="form.supplierAddressShow">
                      {{ form.supplierAddressFormat }}
                      <el-icon
                        v-if="form.supplierFullAddress"
                        @click="form.supplierAddressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierAddressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.supplierFullAddress ? form.supplierFullAddress : "-" }}
                    </span>
                  </span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              &lt;!&ndash; 供应商联系人 &ndash;&gt;
              <el-form-item :label="$t('quickOutbound.label.supplierPerson')">
                {{ form.supplierNameShow ? (form.supplierContactPerson ? form.supplierContactPerson : '-') : encryptName(form.supplierContactPerson) }}
                <el-icon
                  v-if="form.supplierContactPerson"
                  @click="form.supplierContactPerson ? getSupplierRealName() : ''"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                >
                  <component :is="form.supplierNameShow ? '' : 'View'" />
                </el-icon>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              &lt;!&ndash; 供应商联系电话 &ndash;&gt;
              <el-form-item :label="$t('quickOutbound.label.supplierMobile')">
                  <span class="encryptBox">
                    <span v-if="form.supplierContactMobile">
                      {{ form.supplierContactAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.supplierContactMobile && form.supplierContactMobile.length <= 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.supplierContactMobile && form.supplierContactMobile.length > 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                      <el-icon
                        v-if="form.supplierContactMobile"
                        @click="form.supplierMobilePhoneShow ? getSupplierRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierMobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 合同名称&ndash;&gt;
            <el-col :span="6">
              &lt;!&ndash;                cursor:pointer&ndash;&gt;
              <el-form-item :label="$t('quickOutbound.label.contractName')" >
                <span style="color: #762ADB;" v-if="form.contractName">{{form.contractName}}</span>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 合同编码&ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.contractCode')" >
                <span style="color: #762ADB;" v-if="form.contractCode">{{form.contractCode}}</span>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 合同分类&ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.contractClassification')" >
                <span v-if="form.contractType == 1">销售合同</span>
                <span v-else-if="form.contractType == 2">采购合同</span>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 结算方式&ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.settlementMethod')" >
                {{ form.paymentType == 1 ? $t('quickOutbound.label.presentSettlement') : form.paymentType == 2 ? $t('quickOutbound.label.accountHanging') : "-" }}
              </el-form-item>
            </el-col>
            &lt;!&ndash; 要求到货时间 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.plannedReceivedTime')">
                <span v-if="form.plannedReceivedTime">{{ parseTime(form.plannedReceivedTime,"{y}-{m}-{d} {h}:{i}:{s}") }}</span>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 计划发货时间 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.plannedDeliveryTime')">
                <span v-if="form.plannedDeliveryTime">{{ parseTime(form.plannedDeliveryTime,"{y}-{m}-{d} {h}:{i}:{s}") }}</span>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            &lt;!&ndash; 计划配送方式 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.plannedDistributionMethod')">
                {{form.deliveryName ? form.deliveryName : "-"}}
              </el-form-item>
            </el-col>
            &lt;!&ndash; 来源单备注 &ndash;&gt;
            <el-col :span="6">
              <el-form-item :label="$t('quickOutbound.label.sourceOrderRemark')">
                {{form.remark ? form.remark : "-"}}
              </el-form-item>
            </el-col>
          </el-row>-->
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("quickOutbound.label.detailList") }}
            </div>
          </div>
          <div class="panelContent">
            <div class="panelItem" @click="changePanel(item)" :class="{ active: item.active }" v-for="(item, index) in tabs" :key="item.key">
              {{ item.value }}
            </div>
          </div>
          <div v-if="checkType == 1">
            <el-table show-summary border :summary-method="getSummaries" :data="form.warehouseOutboundDetailVOList" highlight-current-row stripe>
              <el-table-column fixed="left" type="index" :label="$t('common.sort')" width="60" align="center"/>
              <el-table-column v-for="col in showProductColumns" :key="col.prop" :prop="col.prop" :label="col.label" :width="col.width" :align="col.align" :show-overflow-tooltip="col.showTooltip">
                <template #default="scope" v-if="col.prop == 'productInfo'">
                  <div style="word-break: break-all"><span style="color: #90979E;">{{ scope.row.productCode }} | </span><span style="color:#52585F">{{ scope.row.productName }}</span></div>
                </template>
                <template #default="scope" v-if="col.prop == 'goodsCategory'">
                  {{ scope.row.firstCategoryName }}/ {{ scope.row.secondCategoryName }}/ {{ scope.row.thirdCategoryName }}
                </template>
                <template #default="scope" v-if="col.prop == 'returnQty'">
                  <span v-if="scope.row.returnQty">{{ scope.row.returnQty }}</span>
                  <span v-else>-</span>
                </template>
                <template #default="scope" v-if="col.prop == 'salePrice'">
                  <span v-if="scope.row.salePrice">{{ scope.row.salePrice }}元/{{scope.row.productUnitName}}</span>
                  <span v-else>-</span>
                </template>
                <template #default="scope" v-if="col.prop == 'saleAmount'">
                  <span v-if="scope.row.saleAmount">{{ scope.row.saleAmount }}</span>
                  <span v-else>-</span>
                </template>
                <template #default="scope" v-if="col.prop == 'planProductQty'">
                  <span v-if="scope.row.planProductQty">{{ scope.row.planProductQty }}{{ scope.row.productUnitName }}</span>
                  <span v-else>-</span>
                </template>
                <template #default="scope" v-if="col.prop == 'planProductWeight'">
                  <span v-if="scope.row.planProductWeight">{{ scope.row.planProductWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
                  <span v-else>-</span>
                </template>
                <template #default="scope" v-if="col.prop == 'alreadyOutboundQty'">
                  <span v-if="scope.row.alreadyOutboundQty">{{ scope.row.alreadyOutboundQty }}{{ scope.row.productUnitName }}</span>
                  <span v-else>-</span>
                </template>
                <template #default="scope" v-if="col.prop == 'alreadyOutboundWeight'">
                  <span v-if="scope.row.alreadyOutboundWeight">{{ scope.row.alreadyOutboundWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
                  <span v-else>-</span>
                </template>
                <template #default="scope" v-if="col.prop == 'remark'">
                  <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <!--<el-table-column fixed="left" type="index" :label="$t('common.sort')" width="60" align="center"/>
              <el-table-column :label="$t('quickOutbound.label.productInfo')" width="250px">
                <template #default="scope">
                  <div style="word-break: break-all"><span style="color: #90979E;">{{ scope.row.productCode }} | </span><span style="color:#52585F">{{ scope.row.productName }}</span></div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.goodsCategory')" min-width="150" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.firstCategoryName }}/ {{ scope.row.secondCategoryName }}/ {{ scope.row.thirdCategoryName }}</template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.productSpecs')" prop="productSpecs" show-overflow-tooltip/>
              <el-table-column :label="$t('quickOutbound.label.commodityProperty')" prop="attributeTypeName" show-overflow-tooltip/>
              <el-table-column :label="$t('quickOutbound.label.returnVolume')" min-width="120" prop="returnQty" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.returnQty">{{ scope.row.returnQty }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.salesUnitPrice')" min-width="120" align="right" prop="salePrice" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.salePrice">{{ scope.row.salePrice }}元/{{scope.row.productUnitName}}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.salesAmount')" min-width="120" align="right" prop="saleAmount" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.saleAmount">{{ scope.row.saleAmount }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.plannedQuantity')" min-width="120" prop="planProductQty" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.planProductQty">{{ scope.row.planProductQty }}{{ scope.row.productUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.plannedConversionQuantity')" min-width="150" prop="planProductWeight" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.planProductWeight">{{ scope.row.planProductWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.outboundQuantity')" min-width="120" prop="alreadyOutboundQty" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.alreadyOutboundQty">{{ scope.row.alreadyOutboundQty }}{{ scope.row.productUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.outboundConversionQuantity')" min-width="150" prop="alreadyOutboundWeight" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.alreadyOutboundWeight">{{ scope.row.alreadyOutboundWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.remark')" min-width="120" prop="remark" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>-->
            </el-table>
          </div>
          <div v-else class="table-container">
            <el-table
              :data="form.outboundPickingListVOList"
              :span-method="spanMethod"
              default-expand-all
              row-class-name="table-row"
              style="border: none; --el-table-border-color: none"
            >
              <template #empty>
                <Empty />
              </template>

              <el-table-column
                type="expand"
                width="1"
              >
                <template #default="props">
                  <el-table
                    :data="props.row.outboundPickingListProductVOList"
                    class="table-children"
                    :show-header="false"
                    :cell-style="cellStyleChildren"
                    show-summary
                    :summary-method="getSummaries1"
                  >
                    <el-table-column
                      type="index"
                      :label="$t('common.sort')"
                      width="50"
                      prop="index"
                    >
                    </el-table-column>
                    <el-table-column v-for="col in showOutboundColumns" :key="col.prop" :prop="col.prop" :label="col.label" :width="col.width" :align="col.align" :show-overflow-tooltip="col.showTooltip">
                      <template #default="scope" v-if="col.prop=='productInfo'">
                        <div style="word-break: break-all">{{ scope.row.productCode }} |{{ scope.row.productName }}</div>
                      </template>
                      <template #default="scope" v-if="col.prop=='goodsCategory'">
                        {{ scope.row.firstCategoryName }} / {{ scope.row.secondCategoryName }} / {{ scope.row.thirdCategoryName }}
                      </template>
                      <template #default="scope" v-if="col.prop=='salePrice'">
                        <span v-if="scope.row.salePrice">{{scope.row.salePrice}}元/{{scope.row.pricingScheme == 0 ? scope.row.productUnitName : scope.row.conversionRelSecondUnitName}}</span>
                        <span v-else>-</span>
                      </template>
                      <template #default="scope" v-if="col.prop=='actualPickThisTime'">
                        {{scope.row.actualPickThisTime}}{{scope.row.productUnitName}}
                      </template>
                      <template #default="scope" v-if="col.prop=='warehouseAreaActualPickWeight'">
                        {{scope.row.warehouseAreaActualPickWeight}}{{scope.row.conversionRelSecondUnitName}}
                      </template>
                    </el-table-column>
                    <!--<el-table-column
                      :label="$t('quickOutbound.label.productInfo')"
                      prop="productName"
                      width="150px"
                    >
                      <template #default="scope">
                        <div style="word-break: break-all">{{ scope.row.productCode }} |{{ scope.row.productName }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.goodsCategory')"
                      prop="firstCategoryName"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{ scope.row.firstCategoryName }} /
                        {{ scope.row.secondCategoryName }} /
                        {{ scope.row.thirdCategoryName }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.productSpecs')"
                      prop="productSpecs"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      v-if="form.outboundType == 7 || form.outboundType == 8"
                      :label="$t('quickOutbound.label.unitPrice')"
                      prop="salePrice"
                      align="right"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        <span v-if="scope.row.salePrice">{{scope.row.salePrice}}元/{{scope.row.pricingScheme == 0 ? scope.row.productUnitName : scope.row.conversionRelSecondUnitName}}</span>
                        <span v-else>-</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="form.outboundType == 7 || form.outboundType == 8"
                      :label="$t('quickOutbound.label.amount')"
                      prop="saleAmount"
                      align="right"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column :label="$t('quickOutbound.label.documentNumber')" prop="docketCode" show-overflow-tooltip></el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.outboundQuantity')"
                      prop="actualPickThisTime"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{scope.row.actualPickThisTime}}{{scope.row.productUnitName}}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.outboundConversionQuantity')"
                      prop="warehouseAreaActualPickWeight"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{scope.row.warehouseAreaActualPickWeight}}{{scope.row.conversionRelSecondUnitName}}
                      </template>
                    </el-table-column>
                    &lt;!&ndash; 品牌 &ndash;&gt;
                    <el-table-column :label="$t('quickOutbound.label.brand')" prop="productBrandName" show-overflow-tooltip/>
                    <el-table-column
                      :label="$t('quickOutbound.label.outboundStorageArea')"
                      prop="warehouseAreaCode"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{scope.row.warehouseAreaName}}
                        &lt;!&ndash;|{{scope.row.warehouseAreaCode}}&ndash;&gt;
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.goodsPackaging')"
                      prop="productPackage"
                      show-overflow-tooltip
                    ></el-table-column>-->
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="50"
              >
                <template #default="scope">
                  <div class="table-header">
                    <div class="sort">
                      {{
                      scope.$index >= 9
                      ? scope.$index + 1
                      : "0" + (scope.$index + 1)
                      }}
                    </div>
                    <div class="column" style="min-width: 200px">
                      {{ $t("quickOutbound.label.pickingList") }}:
                      {{ scope.row.pickingListCode ? scope.row.pickingListCode : '-' }}
                    </div>
                    <div class="column" style="min-width: 235px">
                      {{ $t("quickOutbound.label.actualDistributionMethod") }}:
                      {{ scope.row.deliveryName ? scope.row.deliveryName : '-' }}
                    </div>
                    <div class="column" style="min-width: 235px">
                      {{ $t("quickOutbound.label.actualOutboundTime") }}:
                      {{scope.row.outboundTime ? parseTime(scope.row.outboundTime, "{y}-{m}-{d} {h}:{i}:{s}"):'-'}}
                    </div>
                    <div class="column" style="min-width: 150px">
                      {{$t('quickOutbound.label.carrier')}}:
                      {{ scope.row.carrier ? scope.row.carrier : '-' }}
                    </div>
                    <div class="column" style="margin-left: 5%">
                      {{$t('quickOutbound.label.vehicleNumber')}}:
                      {{ scope.row.carNumber ? scope.row.carNumber : '-' }}
                    </div>
                    <div class="column">
                      {{$t('quickOutbound.label.weighbillNumber')}}:
                      {{ scope.row.poundCode ? scope.row.poundCode : '-' }}
                    </div>
                    <div class="column">
                      {{$t('quickOutbound.label.outboundRemark')}}:
                      {{ scope.row.remark ? scope.row.remark : '-' }}
                    </div>
                    <div class="column" style="margin-right: 5px">
                      <div v-if="scope.row.poundAttachmentFiles">
                        <el-badge
                          :value="JSON.parse(scope.row.poundAttachmentFiles).length"
                          :offset="[10, 8]"
                          class="item"
                          type="primary"
                        >
                          <el-button
                            type="primary"
                            link
                            @click="openAttachment(scope.row.poundAttachmentFiles)"
                          >
                            {{ $t("quickOutbound.label.attachment") }}
                          </el-button>
                        </el-badge>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column v-for="col in showOutboundColumns" :key="col.prop" :prop="col.prop" :label="col.label" :width="col.width" :align="col.align" :show-overflow-tooltip="col.showTooltip">
              </el-table-column>
              <!--<el-table-column
                :label="$t('quickOutbound.label.productInfo')"
                prop="productName"
                width="150px"
              >
              </el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.goodsCategory')"
                prop="goodsCategory"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.productSpecs')"
                prop="productSpecs"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                v-if="form.outboundType == 7 || form.outboundType == 8"
                :label="$t('quickOutbound.label.unitPrice')"
                prop="salePrice"
                align="right"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                v-if="form.outboundType == 7 || form.outboundType == 8"
                :label="$t('quickOutbound.label.amount')"
                prop="saleAmount"
                align="right"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column :label="$t('quickOutbound.label.documentNumber')" prop="docketCode" show-overflow-tooltip></el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.outboundQuantity')"
                prop="actualPickThisTime"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.outboundConversionQuantity')"
                prop="warehouseAreaActualPickWeight"
                show-overflow-tooltip
              ></el-table-column>
              &lt;!&ndash; 品牌 &ndash;&gt;
              <el-table-column :label="$t('quickOutbound.label.brand')" prop="productBrandName" show-overflow-tooltip/>
              <el-table-column
                :label="$t('quickOutbound.label.outboundStorageArea')"
                prop="warehouseAreaName"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.goodsPackaging')"
                prop="productPackage"
                show-overflow-tooltip
              ></el-table-column>-->
            </el-table>

          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">
          {{ $t("common.reback") }}
        </el-button>
      </div>
    </div>
    <UploadDialog
      v-model:visible="uploadDialog.visible"
      ref="uploadDialogRef"
      :showUploadBtn="false"
    />
  </div>
</template>

<script setup lang="ts">
import CommonAPI from "@/modules/wms/api/common";

defineOptions({
  name: "DetailQuickOutbound",
  inheritAttrs: false,
});
import OutboundNoticeAPI from "@/modules/wms/api/outboundNotice";
import QuickOutboundApi, {detailResponse} from "@/modules/wms/api/quickOutbound";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import { parseTime,parseDateTime } from "@/core/utils/index.js";
import UploadDialog from "./components/uploadDialog.vue";

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const uploadDialog = reactive({
  visible: false,
});
const firstTableShow = ref(true);
function closeFirstTable() {
  firstTableShow.value = false
}
function openFirstTable() {
  firstTableShow.value = true
}
const uploadDialogRef = ref();
const loading = ref(false);
const form = reactive<detailResponse>({
  warehouseOutboundDetailVOList: [],
  outboundPickingListVOList: [],
});
const checkType = ref(1);
const tabs = ref([
  {
    key:1,
    value:t('quickOutbound.label.productDetail'),
    active: true
  },
  {
    key:2,
    value:t('quickOutbound.label.outboundDetail'),
    active: false
  },
]);
const showProductColumns = ref([])
const allProductColumns = ref([
  {
    prop:'productInfo',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.productInfo'),
    isShow:true,
    width:'250',
    showTooltip:false,
    align:'left',
  },
  {
    prop:'goodsCategory',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.goodsCategory'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'productSpecs',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.productSpecs'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'attributeTypeName',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.commodityProperty'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'returnQty',
    modulePageFieldCode:'quick_stock_out_detail_product_list_return_quantity',
    label:t('quickOutbound.label.returnVolume'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'salePrice',
    modulePageFieldCode:'quick_stock_out_detail_product_list_sales_price',
    label:t('quickOutbound.label.salesUnitPrice'),
    isShow:true,
    showTooltip:true,
    align:'right',
  },
  {
    prop:'saleAmount',
    modulePageFieldCode:'quick_stock_out_detail_product_list_sales_amount',
    label:t('quickOutbound.label.salesAmount'),
    isShow:true,
    showTooltip:true,
    align:'right',
  },
  {
    prop:'planProductQty',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.plannedQuantity'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'planProductWeight',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.plannedConversionQuantity'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'alreadyOutboundQty',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.outboundQuantity'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'alreadyOutboundWeight',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.outboundConversionQuantity'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'remark',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.remark'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
])
const showOutboundColumns = ref([])
const allOutboundColumns = ref([
  {
    prop:'productInfo',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.productInfo'),
    isShow:true,
    width:'250',
    showTooltip:false,
    align:'left',
  },
  {
    prop:'goodsCategory',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.goodsCategory'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'productSpecs',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.productSpecs'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'salePrice',
    modulePageFieldCode:'quick_stock_out_detail_outbound_detail_sales_price',
    label:t('quickOutbound.label.unitPrice'),
    isShow:true,
    showTooltip:true,
    align:'right',
  },
  {
    prop:'saleAmount',
    modulePageFieldCode:'quick_stock_out_detail_outbound_detail_sales_amount',
    label:t('quickOutbound.label.amount'),
    isShow:true,
    showTooltip:true,
    align:'right',
  },
  {
    prop:'docketCode',
    modulePageFieldCode:'quick_stock_out_detail_outbound_detail_bill_number',
    label:t('quickOutbound.label.documentNumber'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'actualPickThisTime',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.outboundQuantity'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'warehouseAreaActualPickWeight',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.outboundConversionQuantity'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'productBrandName',
    modulePageFieldCode:'quick_stock_out_detail_outbound_detail_brand',
    label:t('quickOutbound.label.brand'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'warehouseAreaName',
    modulePageFieldCode:'',
    label:t('quickOutbound.label.outboundStorageArea'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },
  {
    prop:'productPackage',
    modulePageFieldCode:'quick_stock_out_detail_outbound_detail_goods_packing',
    label:t('quickOutbound.label.goodsPackaging'),
    isShow:true,
    showTooltip:true,
    align:'left',
  },

])
function strategyTenantQueryListMain() {
  let params = {
    moduleCode:'quick_stock_out',
    modulePageCode:'quick_stock_out_detail_main',
  }
  CommonAPI.strategyTenantQueryList(params).then((res)=>{

  }).finally(()=>{

  })
}
function strategyTenantQueryListProduct() {
  let params = {
    moduleCode:'quick_stock_out',
    modulePageCode:'quick_stock_out_detail_product_list',
  }
  CommonAPI.strategyTenantQueryList(params).then((res)=>{
    showProductColumns.value = []
    if(res && res.length){
      for(let i = 0; i < allProductColumns.value.length; i++){
        let all = allProductColumns.value[i]
        res.forEach(r=>{
          if(all.modulePageFieldCode == r.modulePageFieldCode){
            all.isShow = r.isShow
          }
        })
        if(all.isShow){
          showProductColumns.value.push(all)
        }
      }
    }
  }).finally(()=>{

  })
}
function strategyTenantQueryListOutbound() {
  let params = {
    moduleCode:'quick_stock_out',
    modulePageCode:'quick_stock_out_detail_outbound_detail',
  }
  CommonAPI.strategyTenantQueryList(params).then((res)=>{
    showOutboundColumns.value = []
    if(res && res.length){
      for(let i = 0; i < allOutboundColumns.value.length; i++){
        let all = allOutboundColumns.value[i]
        res.forEach(r=>{
          if(all.modulePageFieldCode == r.modulePageFieldCode){
            all.isShow = r.isShow
          }
        })
        if(all.isShow){
          showOutboundColumns.value.push(all)
        }
      }
    }
  }).finally(()=>{

  })
}
const changePanel = (data) => {
  tabs.value.map(item => item.active = false);
  data.active =true;
  checkType.value = data.key
}
async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}
/**
 * 合并列
 * @param row
 * @param column
 * @param rowIndex
 * @param columnIndex
 */
function spanMethod({ row, column, rowIndex, columnIndex }: any) {
  if (columnIndex === 1) {
    return {
      rowspan: 1,
      colspan: 99,
    };
  } else {
    return {
      rowspan: 0,
      colspan: 0,
    };
  }
}
/**
 * 子表格样式
 */
function cellStyleChildren({ row, column, rowIndex, columnIndex }: any) {
  if ([0, 1, 2, 3, 4, 5].includes(columnIndex) && row.isChildren) {
    return {
      visibility: "hidden",
      border: "none",
    };
  }
  return {
    border: "none",
    borderTop: "1px solid #E5E9F2",
  };
}
function openAttachment(files) {
  uploadDialog.visible = true;
  uploadDialogRef.value.showUploadBtn = false;
  uploadDialogRef.value.setEditType("detail");
  uploadDialogRef.value.setFormData(JSON.parse(files));
}

/* 姓名加密 */
function encryptName(name: any) {
  if (!name?.trim()) return "-";

  const firstChar = name[0]; // 获取第一位
  const shouldShowFourStars = name.length > 5;

  return shouldShowFourStars
    ? `${firstChar}****`
    : `${firstChar}${"*".repeat(name.length - 1)}`;
}

// 获取真实客户联系人
function getRealName() {
  form.nameShow = true;
}
//获取真实供应商联系人
function getSupplierRealName() {
  form.supplierNameShow = true;
}

/** 查询出库通知单详情 */
function queryDetail() {
  loading.value = true;
  let params = {
    id: route.query.id,
  };
  QuickOutboundApi.queryFastDetailEncrypt(params)
    .then((data: any) => {
      Object.assign(form, data);
      form.mobilePhoneShow = true;
      form.supplierMobilePhoneShow = true;
      form.fullAddress = `${form.countryName || ''}${form.provinceName || ''}${form.cityName || ''}${form.districtName || ''}${form.address || ''}`;
      form.supplierFullAddress = `${form.supplierCountryName  || ''}${form.supplierProvinceName  || ''}${form.supplierCityName  || ''}${form.supplierDistrictName  || ''}${form.supplierAddress || ''}`;
      if (form.contactPerson?.length > 1) {
        form.nameShow = false;
      } else {
        form.nameShow = true;
      }
      if(form.supplierContactPerson?.length > 1){
        form.supplierNameShow = false;
      }else {
        form.supplierNameShow = true;
      }

      // 地址包含数字
      if (form.fullAddress && containsNumber(form.fullAddress)) {
        form.addressShow = true;
        form.addressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      } else {
        // 不包含数字
        form.addressShow = false;
      }
      if(form.supplierFullAddress  && containsNumber(form.supplierFullAddress)) {
        form.supplierAddressShow = true;
        form.supplierAddressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      }else {
        form.supplierAddressShow = false;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function getSummaries(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计：';
      return;
    }
    else if(column.label == t('quickOutbound.label.salesAmount')){
      sums[index] = form.totalSaleAmount || '-'
    }else if(column.label == t('quickOutbound.label.plannedQuantity')){
      sums[index] = form.totalPlanProductQty || '-'
    }else if(column.label == t('quickOutbound.label.outboundQuantity')){
      sums[index] = form.totalOutboundQty || '-'
    }else {
      sums[index] = ' '
    }
  })
  return sums;
}
function getSummaries1(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 3) {
      sums[index] = '合计：';
      return;
    } else if(column.label == t('quickOutbound.label.outboundQuantity') || column.label == t('quickOutbound.label.outboundConversionQuantity') || column.label == t('quickOutbound.label.amount')){
      const values = data.map(item => Number(item[column.property]));
      if (!values.every(value => isNaN(value))) {
        sums[index] = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0);
      }
    }else {
      sums[index] = ' '
    }
  })
  return sums;
}
function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}
function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}

// 获取真实客户联系电话
function getRealPhone() {
  OutboundNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.customerMobile = data.mobile;
      form.mobilePhoneShow = false;
    })
    .finally(() => {});
}
//获取真实供应商联系电话
function getSupplierRealPhone(){
  OutboundNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.supplierContactMobile = data.supplierContactMobile;
      form.supplierMobilePhoneShow = false;
    })
    .finally(() => {});
}

onMounted(() => {
  queryDetail();
  strategyTenantQueryListMain()
  strategyTenantQueryListProduct()
  strategyTenantQueryListOutbound()
});
</script>
<style scoped lang="scss">
.outboundNoticeDetail {
  background: #ffffff;
  border-radius: 4px;
  .table-container {
    padding: 0 20px;
    margin-bottom: 10px;

    .table-header {
      display: flex;
      justify-content: flex-start;
      background: #F6F0FF !important;
      font-weight: 400 !important;
      border: 1px solid #e5e9f2;
      position: relative;

      font-weight: 600;
      font-size: 14px;
      color: #151719;
      padding: 15px;
      margin: 0 -20px 0 -20px;
      align-items: center;

      .sort {
        padding: 0 6px;
        background-color: #762ADB;
        border-radius: 4px;
        font-size: 14px;
        color: #ffffff;
        margin-right: 30px;
      }

      .column {
        padding-right: 10px;
        word-break: break-all;
      }

      .action {
        position: absolute;
        right: 10px;
        top: 10px;
        color: #762adb;
        cursor: pointer;
        background-color: #f4f6fa;
        padding: 10px 20px;
      }
    }

    :deep(.table-row) {
      padding: 0 !important;
      margin: 0 !important;

      > td {
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
      }
    }

    :deep(.el-table__expand-icon .el-icon) {
      display: none;
    }

    :deep(.el-table__header) {
      margin-bottom: 16px;
      border: 1px solid #e5e9f2;
    }

    .table-children {
      margin-top: -20px;
      border: 1px solid #e5e9f2;
    }

    :deep(.label-required .cell) {
      &:before {
        content: "*";
        color: red;
        margin-right: 5px;
      }
    }

    :deep(.summaries) {
      font-weight: bold;
    }
  }
  .td-label{
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #90979E;
    line-height: 24px;
    text-align: right;
    font-style: normal;
    background: #F4F6FA;
    width: 110px;
    text-align: right;
  }
  .td-value{
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    text-align: left;
    word-break: break-word;
  }
  td{
    border: 1px solid #E5E7F3;
    padding: 4px 8px;
  }
}
:deep(.el-form-item){
  margin-bottom: 0px;
}
:deep(.el-form-item__label){
  background: #F4F6FA;
  box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
  font-weight: 400!important;
  font-size: 12px!important;
  color: #90979E!important;
}
:deep(.el-form-item__content){
  padding-left: 8px;
  background: #FFFFFF;
  font-size: 12px!important;
  box-shadow: inset -1px -1px 0px 0px #E5E7F3, inset 0px 1px 0px 0px #E5E7F3;
}
:deep(.el-form-item--default){
  margin-bottom: 0px !important;
}
</style>
<style lang="scss">
.outboundNoticeDetail {
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .panelContent {
    display: flex;
    border-bottom: 1px solid #F2F3F4;
    width: 100%;
    margin-bottom: 16px;
    .panelItem {
      font-size: 14px;
      color: #151719;
      padding: 10px 39px;
      cursor: pointer;
      &.active {
        color: var(--el-color-primary);
        border-bottom: 2px solid var(--el-color-primary);
      }
    }
  }
  .title-div{
    background: #F4F6FA !important;
    border-radius: 2px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #52585f;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding:15px 0px;
  }
  .data-div{
    border-radius: 2px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #90979E;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding:15px 0px;
  }
  .text-center{
    text-align: center;
  }
  .text-right{
    text-align: right;
  }
}
</style>
