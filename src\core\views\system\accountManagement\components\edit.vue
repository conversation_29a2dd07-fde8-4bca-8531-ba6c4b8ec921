<!--
 * @Author: <PERSON><PERSON><PERSON> cheng<PERSON>@yto.net.cn
 * @Date: 2025-01-15 14:05:33
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-02-20 21:00:53
 * @FilePath: \supply-manager-web\src\core\views\system\accountManagement\components\edit.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-drawer
    :value="isVisible"
    :title="title"
    :close-on-click-modal="false"
    width="500px"
    @close="close"
  >
    <el-form
      :model="userForm"
      :rules="rules"
      ref="accountRef"
      label-position="top"
    >
      <el-form-item
        v-if="editType === 'edit'"
        :label="$t('accountManagement.label.accountId')"
        prop="account"
      >
        <el-input
          type="text"
          :placeholder="$t('common.placeholder.inputTips')"
          v-model="userForm.account"
          disabled
        />
      </el-form-item>
      <el-form-item :label="$t('accountManagement.label.name')" prop="nickName">
        <el-input
          type="text"
          :placeholder="$t('common.placeholder.inputTips')"
          v-model="userForm.nickName"
          :maxlength="20"
          clearable
        />
      </el-form-item>

      <el-form-item :label="$t('accountManagement.label.phone')" prop="mobile">
        <el-input
          v-model="userForm.mobile"
          :placeholder="$t('common.placeholder.inputTips')"
          :maxlength="30"
        >
          <template #prepend>
            <el-select v-model="userForm.countryAreaCode" style="width: 80px">
              <el-option
                v-for="(item, index) in countryNumCodeList"
                :key="index"
                :label="item.internationalCode"
                :value="item.internationalCode"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item
        :label="$t('accountManagement.label.employeeNo')"
        prop="employeeNo"
      >
        <el-input
          type="text"
          :placeholder="$t('common.placeholder.inputTips')"
          v-model="userForm.employeeNo"
          :maxlength="10"
          @input="handleInput"
          clearable
        />
      </el-form-item>
      <el-form-item
        :label="$t('accountManagement.label.password')"
        prop="password"
      >
        <el-input
          type="text"
          :placeholder="$t('common.placeholder.inputTips')"
          v-model="userForm.password"
          disabled
        />
      </el-form-item>
      <!--<el-form-item
        :label="$t('accountManagement.label.affiliatedDepartment')"
        prop="deptId"
      >
        <el-cascader
          v-model="userForm.deptId"
          :options="deptList"
          :props="{ value: 'id', label: 'deptName', checkStrictly: true}"
          popper-class="account-management-dept-cascader"
          clearable
        />
      </el-form-item>-->
      <el-form-item
        :label="$t('accountManagement.label.affiliatedDepartment')"
        prop="deptIds"
      >
        <!--<el-cascader
          v-model="userForm.deptIds"
          :options="deptList"
          :props="{ value: 'id', label: 'deptName', checkStrictly: true, multiple: true, children: 'children',emitPath: false}"
          clearable
          @change="setDeptIds"
          ref="deptRef"
          multiple
        />-->
        <el-tree-select
          v-model="userForm.deptIds"
          :data="deptList"
          :props="{ value: 'id', label: 'deptName', children: 'children', }"
          node-key="id"
          multiple
          clearable
          :render-after-expand="false"
          show-checkbox
          check-strictly
          check-on-click-node
        />
      </el-form-item>
      <el-form-item :label="$t('accountManagement.label.roles')" prop="roleIds">
        <el-select
          multiple
          filterable
          v-model="userForm.roleIds"
          :placeholder="$t('common.placeholder.selectTips')"
          clearable
        >
          <el-option
            v-for="item in roleList"
            :key="item.roleId"
            :label="item.roleName"
            :value="item.roleId"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">
          {{ $t("common.confirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>
<script setup>
import UserAPI /*UserForm*/ from "@/core/api/accountManagement.ts";
import { ElMessage } from "element-plus";

const emit = defineEmits(["onSubmit"]);
const { proxy } = getCurrentInstance();
const { t } = useI18n();

// defineProps({
//   isVisible: {
//     type: Boolean,
//     default: false,
//   },
//   title: {
//     type: String,
//     default: "",
//   },
// });

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});

function initForm() {
  return {
    account: "",
    nickName: "",
    mobile: "",
    employeeNo: "",
    password: "********",
    deptId: [],
    roleIds: [],
    status: 1,
    userId: "",
    countryAreaCode: "+86",
  };
}

let data = reactive({
  userForm: initForm(),
  rules: {
    account: [
      {
        required: true,
        message: proxy.$t("accountManagement.rules.account"),
        trigger: "blur",
      },
    ],
    nickName: [
      {
        required: true,
        message: proxy.$t("accountManagement.rules.name"),
        trigger: "blur",
      },
      {
        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
        message: proxy.$t("accountManagement.rules.nameFomart"),
        trigger: ["change", "blur"],
      },
    ],
    mobile: [
      {
        required: true,
        message: proxy.$t("accountManagement.rules.phone"),
        trigger: "blur",
      },
      // {
      //   pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
      //   message: proxy.$t("accountManagement.rules.phoneFomart"),
      //   trigger: ["blur", "change"],
      // },
    ],
    employeeNo: [
      {
        pattern: /^[0-9]{1,10}$/,
        message: proxy.$t("accountManagement.rules.employeeNoFomart"),
      },
    ],
    /*deptId: [
      {
        required: true,
        message: proxy.$t("accountManagement.rules.department"),
        trigger: ["blur", "change"],
      },
    ],*/
    deptIds: [
      {
        required: true,
        message: proxy.$t("accountManagement.rules.department"),
        trigger: ["blur", "change"],
      },
    ],
    roleIds: [
      {
        required: true,
        message: proxy.$t("accountManagement.rules.roleIds"),
        trigger: ["blur", "change"],
      },
    ],
  },
  submitLoading: false,
  editType: "",
  deptList: [],
});
const deptRef = ref()

let { userForm, rules, submitLoading, editType, deptList } = toRefs(data);

// let dialogVisible = ref(false);

const countryNumCodeList = ref([]);

const isVisible = computed({
  get: () => props.visible,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

let roleList = ref([]);
let accountRef = ref();

// 获取区号
function getAreaList() {
  UserAPI.getAllCountry()
    .then((data) => {
      countryNumCodeList.value = data;
    })
    .finally(() => {});
}

function queryAllRoleList() {
  let params = {
    PlatformType: "supply",
  };
  UserAPI.allRoleList(params).then((data) => {
    roleList.value = data;
  });
}

function setDeptIds(values) {
  console.log("values====",values)
  // 找出所有需要禁用的父节点ID
  const disabledParentIds = findParentIds(values)

  // 更新选项的禁用状态
  updateDisabledStatus(values, disabledParentIds)

  // 过滤掉被禁用的父节点
  userForm.value.deptIds = values.filter(
    id => !disabledParentIds.includes(id)
  )
  /*console.log("values======",values)
  userForm.value.deptIds = []
  // 获取选中的nodeList
  let nodeList = deptRef.value.getCheckedNodes()
  if(nodeList && nodeList.length > 0){
    nodeList.forEach(list=>{
      userForm.value.deptIds.push(list.value)
    })
  }*/
  // 当选中子节点时，找到对应的父节点并设置为不可选
  /*values.forEach(val => {
    const parent = deptList.value.find(opt =>
      opt.children?.some(child => child.id === val)
    )
    if (parent) {
      parent.disabled = true
      // 从选中值中移除父节点
      userForm.value.deptIds = userForm.value.deptIds.filter(v => v !== parent.id)
    }
  })*/
}
function findParentIds(selectedIds, options = deptList.value, parentIds = []) {
  options.forEach(option => {
    if (selectedIds.includes(option.id)) {
      parentIds.push(...getAncestorIds(option.id))
    }
    if (option.children) {
      findParentIds(selectedIds, option.children, parentIds)
    }
  })
  return [...new Set(parentIds)]
}
// 获取某个节点的所有祖先ID
function getAncestorIds(id) {
  const findParent = (options, targetId, ancestors = []) => {
    for (const option of options) {
      if (option.id === targetId) return ancestors
      if (option.children) {
        const found = findParent(option.children, targetId, [...ancestors, option.id])
        if (found) return found
      }
    }
    return null
  }
  return findParent(deptList.value, id) || []
}
function updateDisabledStatus(selectedIds, disabledParentIds) {
  const updateOptions = (options) => {
    return options.map(option => {
      const isDisabled = disabledParentIds.includes(option.id)
      return {
        ...option,
        disabled: isDisabled,
        children: option.children
          ? updateOptions(option.children)
          : undefined
      }
    })
  }
  deptList.value = updateOptions(deptList.value)
}
function getDeptList() {
  UserAPI.allDeptList().then((data) => {
    deptList.value = data;
    /*if(deptList.value && deptList.value.length > 0){
      setDeptIds(userForm.value.deptIds)
    }*/
    /*if (deptList.value && deptList.value.length > 0) {
      userForm.value.deptId = findParentLabel(
        deptList.value,
        userForm.value.deptId
      );
    }*/
  });
}

function findParentLabel(list, str, parents = []) {
  for (let i = 0; i < list.length; i++) {
    const node = list[i];
    if (node.id === str) {
      return [...parents, node.id];
    } else if (node.children && node.children.length > 0) {
      const result = findParentLabel(node.children, str, [...parents, node.id]);
      if (result) {
        return result;
      }
    }
  }
  // 没有找到目标子节点
  return null;
}

function handleInput() {
  // 使用正则表达式只保留数字
  userForm.value.employeeNo = userForm.value.employeeNo.replace(/[^\d]/g, "");
}

function close() {
  isVisible.value = false;
  reset();
}

function reset() {
  accountRef.value.clearValidate();
  accountRef.value.resetFields();
  userForm.value = initForm();
}

function submitForm() {
  if (editType.value === "add") {
    submitAddUser();
  } else if (editType.value === "edit") {
    submitUpdateUser();
  }
}

function submitAddUser() {
  accountRef.value.validate((valid) => {
    if (!valid) return;
    submitLoading.value = true;
    let params = {
      nickName: userForm.value.nickName,
      mobile: userForm.value.mobile,
      employeeNo: userForm.value.employeeNo,
      // deptId: userForm.value.deptId.slice(-1)[0],
      deptIds: userForm.value.deptIds,
      roleIds: userForm.value.roleIds ? userForm.value.roleIds.join(",") : "",
      userType: "scm_sub",
      status: 1,
      loginTypes: ["username", "mobilePassword"],
      countryAreaCode: userForm.value.countryAreaCode,
    };
    UserAPI.addUserInfo(params)
      .then((res) => {
        ElMessage.success(proxy.$t("accountManagement.message.addSucess"));
        emit("submitted");
        isVisible.value = false;
        return res;
      })
      .finally(() => {
        submitLoading.value = false;
      });
  });
}

function submitUpdateUser() {
  // console.log("userForm.value.deptId", userForm.value.deptId);
  // console.log("userForm.value.deptId", userForm.value.deptId.slice(-1)[0]);
  accountRef.value.validate((valid) => {
    if (!valid) return;
    submitLoading.value = true;
    let params = {
      userId: userForm.value.userId,
      account: userForm.value.account,
      nickName: userForm.value.nickName,
      mobile: userForm.value.mobile,
      employeeNo: userForm.value.employeeNo,
      // deptId: userForm.value.deptId.slice(-1)[0],
      deptIds: userForm.value.deptIds,
      roleIds: userForm.value.roleIds ? userForm.value.roleIds.join(",") : "",
      userType: "scm_sub",
      status: userForm.value.status,
      countryAreaCode: userForm.value.countryAreaCode,
    };
    UserAPI.updateUserInfo(params)
      .then((res) => {
        ElMessage.success(proxy.$t("accountManagement.message.editSucess"));
        emit("submitted");
        isVisible.value = false;
        return res;
      })
      .finally(() => {
        submitLoading.value = false;
      });
  });
}

function setEditType(data) {
  editType.value = data;
}

function setFormData(data) {
  userForm.value = { ...data };
  let roleIds = data.baseRoleVOList
    ? data.baseRoleVOList.map((obj) => {
        return obj.roleId;
      })
    : [];
  userForm.value.roleIds = roleIds;
  getRealPhone(userForm.value.userId);
}

function getRealPhone(id) {
  UserAPI.queryRealPhone({ userId: id })
    .then((data) => {
      userForm.value.mobile = data.mobile;
    })
    .finally(() => {});
}

defineExpose({
  setFormData,
  setEditType,
  getDeptList,
  queryAllRoleList,
  getAreaList,
  getRealPhone,
});
</script>

<style scoped lang="scss">
// 确保账户管理页面的部门选择器能正常工作
// 覆盖全局样式中隐藏的radio按钮
</style>
<style lang="scss">
.account-management-dept-cascader {
  .el-cascader-panel .el-radio__input {
    display: block !important;
  }
  .el-cascader-panel .el-radio {
    display: block !important;
  }
}
</style>
