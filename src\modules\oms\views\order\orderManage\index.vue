<template>
    <div class="app-container">
        <div class="orderManage">
            <div class="search-container">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
                    <el-form-item prop="dateRange" :label="$t('omsOrder.label.dateType')">
                        <el-select
                                v-model="queryParams.dateType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                class="!w-[160px]"
                        >
                            <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                        <el-date-picker
                                :editable="false"
                                class="!w-[330px]"
                                v-model="queryParams.dateRange"
                                type="daterange"
                                range-separator="~"
                                start-placeholder="开始时间"
                                end-placeholder="截止时间"
                                value-format="YYYY-MM-DD"
                                :placeholder="$t('common.placeholder.selectTips')"
                        />
                        <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="changeDateRange(1)">{{$t('omsOrder.label.today')}}</span>
                        <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="changeDateRange(2)">{{$t('omsOrder.label.yesterday')}}</span>
                        <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)" @click="changeDateRange(3)">{{$t('omsOrder.label.weekday')}}</span>
                    </el-form-item>
                    <el-form-item prop="orderCode" :label="$t('omsOrder.label.orderCode')">
                        <el-input
                                v-model="queryParams.orderCode"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                maxlength="50"
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="productName" :label="$t('omsOrder.label.productName')">
                        <el-input
                                v-model="queryParams.productName"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                maxlength="50"
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="customerName" :label="$t('omsOrder.label.customerName')">
                        <el-input
                                v-model="queryParams.customerName"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                maxlength="50"
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('omsOrder.label.orderType')" prop="orderType">
                        <el-select
                                v-model="queryParams.orderType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in orderTypeOptionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                    </el-form-item>
                  <el-form-item prop="orderBatchNo" :label="$t('omsOrder.label.deliveryBatch')">
                    <el-input
                      v-model="queryParams.orderBatchNo"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                      maxlength="50"
                      class="!w-[256px]"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('omsOrder.label.syncStatus')" prop="syncStatus">
                    <el-select
                      v-model="queryParams.syncStatus"
                      :placeholder="$t('common.placeholder.selectTips')"
                      clearable
                      class="!w-[256px]"
                    >
                      <el-option v-for="item in syncStatusOptionList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('omsOrder.label.submitterUser')" prop="submitterUser">
                    <el-input
                      v-model="queryParams.submitterUser"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                      maxlength="50"
                      @input="submitterUserChange"
                      @blur="submitterUserChange"
                      class="!w-[256px]"
                    />
                    <el-checkbox class="ml8px" v-model="queryParams.mine" @change="handleMineChange">{{ $t('omsOrder.label.isMine') }}</el-checkbox>
                  </el-form-item>
                    <el-form-item>
                        <el-button v-hasPerm="['oms:order:orderManage:search']" type="primary" @click="handleQuery">
                            {{$t('common.search')}}
                        </el-button>
                        <el-button v-hasPerm="['oms:order:orderManage:reset']"  @click="handleResetQuery">
                            {{$t('common.reset')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-card shadow="never" class="table-container">
                <template #header>
                    <div class="flex-center-but">
                        <div>
                            <el-button v-hasPerm="['oms:order:orderManage:handleSync']" :disabled="!multipleSelection.length" type="primary" @click="handleSyncStatus">
                              {{$t('omsOrder.label.handleSync')}}
                            </el-button>
                            <el-button v-hasPerm="['oms:order:orderManage:add']" type="primary" @click="addOrder(null,null,'add')">
                                {{$t('omsOrder.button.addOrder')}}
                            </el-button>
                        </div>
<!--                        <div>-->
<!--                            <el-button v-hasPerm="['oms:order:orderManage:import']" @click="handleUpload()">-->
<!--                                {{$t('omsOrder.button.importOrderInformation')}}-->
<!--                            </el-button>-->
<!--                        </div>-->
                    </div>
                </template>
                <div class="panelContent">
                    <div class="panelItem" @click="changePanel(item)" :class="{ active: item.active }" v-for="(item, index) in tabs" :key="item.key">
                        {{ item.value }}(
                        <span v-if="item.key === ''">{{countTotal.allCount}}</span>
                        <span v-if="item.key === 0">{{countTotal.draftCount}}</span>
                        <span v-if="item.key === 1">{{countTotal.approveCount}}</span>
                        <span v-if="item.key === 2">{{countTotal.stockUpCount}}</span>
                        <span v-if="item.key === 3">{{countTotal.deliveryCount}}</span>
                        <span v-if="item.key === 4">{{countTotal.completeCount}}</span>
                        <span v-if="item.key === 5">{{countTotal.cancelCount}}</span>
                        )
                    </div>
                </div>
                <el-table
                        v-loading="loading"
                        :data="orderList"
                        highlight-current-row
                        stripe
                        @selection-change="handleSelectionChange"
                        @sort-change="handleSortChange"
                >
                    <template #empty>
                        <Empty/>
                    </template>
                    <el-table-column fixed="left" type="selection" width="60" align="center" />
                    <el-table-column fixed="left" :label="$t('omsOrder.label.orderCode')" prop="orderCode" show-overflow-tooltip min-width="170"></el-table-column>
                    <el-table-column :label="$t('omsOrder.label.deliveryBatch')" prop="orderBatchNo" show-overflow-tooltip min-width="170">
                      <template #default="scope">
                        <span>{{ scope.row.orderBatchNo || '-' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('omsOrder.label.orderType')" prop="orderType" show-overflow-tooltip min-width="100">
                      <template #default="scope">
                        <span v-if="scope.row.orderType">{{ getOrderTypeLabel(scope.row.orderType) }}</span>
                        <span v-else>-</span>
                    </template>
                    </el-table-column>
                    <el-table-column :label="$t('omsOrder.label.presale')" prop="isPresale" show-overflow-tooltip min-width="60">
                      <template #default="scope">
                        <span v-if="scope.row.isPresale == 1">{{ t("omsOrder.presaleList[1]") }}</span>
                        <span v-else-if="scope.row.isPresale == 0">{{ t("omsOrder.presaleList[0]") }}</span>
                        <span v-else>-</span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('omsOrder.label.orderTheme')" prop="orderTheme" show-overflow-tooltip min-width="170"></el-table-column>
                    <el-table-column :label="$t('omsOrder.label.customerInformation')" prop="customerName" show-overflow-tooltip min-width="150"></el-table-column>
                    <el-table-column :label="$t('omsOrder.label.product')" prop="allProductName" show-overflow-tooltip min-width="200"></el-table-column>
                    <el-table-column :label="$t('omsOrder.label.productCount')" prop="totalCount" show-overflow-tooltip align="right" min-width="80"></el-table-column>
                    <el-table-column :label="$t('omsOrder.label.orderMoney')" prop="totalAmount" :sortable="'custom'" show-overflow-tooltip align="right" min-width="130">
                        <template #default="scope">
                            <span v-if="scope.row.totalAmount"  style="color:var(--el-color-primary)">
                                <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
                                <span v-else>$</span>
                                <span>{{scope.row.totalAmount }}</span>
                            </span>
                           <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('omsOrder.label.receivingInfo')" min-width="300">
                        <template #default="scope">
                            <div class="flex-center-start">
                                <div style="width: 90%">
                                    <div class="cursor-pointer">
                                        {{ $t("omsOrder.label.receiveName") }}：{{scope.row.contactPerson ? scope.row.contactPerson : '-'}}
                                    </div>
                                    <div class="cursor-pointer">
                                        {{ $t("purchaseRequirements.label.receiveMobile") }}：
                                        <template v-if="scope.row.contactAreaCode && scope.row.contactMobile">
                                            <el-tooltip effect="dark" :content="scope.row.contactAreaCode+ '-'+ scope.row.contactMobile" placement="top">
                                                {{scope.row.contactAreaCode}}-{{ scope.row.contactMobile }}
                                            </el-tooltip>
                                        </template>
                                        <span v-else>-</span>
                                    </div>
                                    <div class="encryptBox">
                                        {{ $t("purchaseRequirements.label.deliveryAddress") }}：
                                        <template v-if="scope.row.deliveryAddress">
                                            <el-tooltip effect="dark" :content="scope.row.deliveryAddress" placement="top">
                                                {{ scope.row.deliveryAddress }}
                                            </el-tooltip>
                                        </template>
                                        <span v-else>-</span>
                                    </div>
                                </div>
                                <div v-hasPermEye="['oms:order:orderManage:eye']" v-if="scope.row.contactPerson || scope.row.contactMobile || scope.row.address" style="width: 10%" class="cursor-pointer">
                                    <el-icon
                                            v-if="!scope.row.isNameDecrypted || !scope.row.isMobileDecrypted || scope.row.deliveryAddress?.includes('*') &&!scope.row.isAddressDecrypted"
                                            @click="handleViewInfo(scope.row)"
                                            v-hasPermEye="['pms:requirements:eye']"
                                            color="#762ADB "
                                            size="16"
                                    >
                                        <View />
                                    </el-icon>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('omsOrder.label.planDeliveryDate')" show-overflow-tooltip min-width="250">
                        <template #default="scope">
                            <span v-if="scope.row.expectedReceivedTimeBase">{{scope.row.expectedReceivedTimeBase}}</span>
                            <span v-if="scope.row.expectedReceivedTimeStar">&nbsp;{{scope.row.expectedReceivedTimeStar}}</span>
                            <span v-if="scope.row.expectedReceivedTimeStar && scope.row.expectedReceivedTimeEnd">~</span>
                            <span v-if="scope.row.expectedReceivedTimeEnd">{{ scope.row.expectedReceivedTimeEnd}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('omsOrder.label.orderDate')" show-overflow-tooltip min-width="180">
                        <template #default="scope">
                            <span v-if="scope.row.submitterTime">{{ parseDateTime(scope.row.submitterTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column :label="$t('omsOrder.label.orderSource')" show-overflow-tooltip min-width="100">-->
<!--                        <template #default="scope">-->
<!--                            <span v-if="scope.row.orderSource==2">{{t('omsOrder.orderSourceList.imports')}}</span>-->
<!--                            <span v-if="scope.row.orderSource==1">{{t('omsOrder.orderSourceList.manuallyAdd')}}</span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                  <el-table-column :label="$t('omsOrder.label.submitterUser')" prop="submitter" show-overflow-tooltip min-width="150"></el-table-column>
                    <el-table-column :label="$t('omsOrder.label.orderStatus')" prop="orderStatus" show-overflow-tooltip min-width="130" >
                        <template #default="scope">
                            <div class="purchase">
                                <div class="purchase-status purchase-status-color0" v-if="scope.row.orderStatus==0">{{t('omsOrder.orderStatusList.draft')}}</div>
                                <div class="purchase-status purchase-status-color1" v-if="scope.row.orderStatus==1">{{t('omsOrder.orderStatusList.check')}}</div>
                                <div class="purchase-status purchase-status-color2" v-if="scope.row.orderStatus==2">{{t('omsOrder.orderStatusList.ready')}}</div>
                                <div class="purchase-status purchase-status-color3" v-if="scope.row.orderStatus==3">{{t('omsOrder.orderStatusList.send')}}</div>
                                <div class="purchase-status purchase-status-color0" v-if="scope.row.orderStatus==4">{{t('omsOrder.orderStatusList.finash')}}</div>
                                <div class="purchase-status purchase-status-color0" v-if="scope.row.orderStatus==5">{{t('omsOrder.orderStatusList.close')}}</div>
                            </div>
                        </template>
                    </el-table-column>
                  <el-table-column v-if="currentActiveTab == 1" :label="$t('omsOrder.button.approveStatus')" prop="approveStatus" show-overflow-tooltip min-width="100">
                    <template #default="scope">
                      <span v-if="scope.row.approveStatus == 0">{{ t('omsOrder.label.approveIng') }}</span>
                      <span v-else-if="scope.row.approveStatus == 1">{{ t('omsOrder.label.approveUnpass') }}</span>
                      <span v-else-if="scope.row.approveStatus == 2">{{ t('omsOrder.label.approvePass') }}</span>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                    <el-table-column :label="$t('omsOrder.label.remark')" prop="remark" show-overflow-tooltip min-width="150"></el-table-column>
                    <el-table-column :label="$t('omsOrder.label.syncStatus')" prop="syncStatus" show-overflow-tooltip min-width="100">
                      <template #default="scope">
                        <span v-if="scope.row.syncStatus">{{ getSyncStatusLabel(scope.row.syncStatus) }}</span>
                        <span v-else>-</span>
                      </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="150">
                        <template #default="scope">
                            <el-button v-hasPerm="['oms:order:orderManage:detail']" type="primary" link @click="detailOrder(scope.row.id,scope.row.orderCode,'detail')">
                                {{$t('common.detailBtn')}}
                            </el-button>
                            <el-button v-hasPerm="['oms:order:orderManage:check']" v-if="scope.row?.canApprove" type="primary" link @click="detailOrder(scope.row.id,scope.row.orderCode,'check')">
                              {{$t('omsOrder.button.check')}}
                            </el-button>
                            <el-button v-hasPerm="['oms:order:orderManage:edit']" v-if="scope.row?.canDeleteOrEdit" type="primary" link @click="addOrder(scope.row.id,scope.row.orderCode,'edit')">
                                {{$t('common.edit')}}
                            </el-button>
                            <el-button v-hasPerm="['oms:order:orderManage:delete']" v-if="scope.row?.canDeleteOrEdit" type="danger" link @click="deleteOrder(scope.row)">
                                {{$t('common.delete')}}
                            </el-button>
                          <el-button v-hasPerm="['oms:order:orderManage:recall']" v-if="scope.row?.canRecall" type="danger" link @click="recallHandler(scope.row.id)">
                            {{$t('omsOrder.button.recall')}}
                          </el-button>
                            <el-button v-hasPerm="['oms:order:orderManage:print']" v-if="scope.row?.canPrint" type="primary" link @click="printOrder(scope.row.id,scope.row.orderCode)">
                              {{$t('omsOrder.button.print')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                        v-if="total > 0"
                        v-model:total="total"
                        v-model:page="queryParams.page"
                        v-model:limit="queryParams.limit"
                        @pagination="handleQuery"
                />
            </el-card>
        </div>
        <!--上传-->
        <UploadDialog
                :key="dialogkey"
                v-model:visible="uploadDialog.visible"
                :submitLoading="submitLoading"
                :isClose="isClose"
                ref="uploadDialogRef"
                @on-submit="onSubmitUpload"
        />


      <el-dialog v-model="dialogSyncStatusVisible" width="600px" :title="$t('omsOrder.message.syncStatusTitle')">
        您所勾选的
        <span v-for="item in unSyncStatusList" :key="item">
          { <span>{{item}}</span> }、
        </span>不符合同步条件，请重新选择
        <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogSyncStatusVisible = false">{{$t('common.cancel')}}</el-button>
        </span>
        </template>
      </el-dialog>

      <Print ref="orderPrintRef" class="display-none" />
    </div>
</template>

<script lang="ts">
    import { defineComponent } from "vue";
    export default defineComponent({
        beforeRouteEnter(to, from, next) {
            let tab=  localStorage.getItem('tabs')
            if((from.path=='/oms/order/addOrder' || from.path=='/oms/order/orderDetail') && tab!==undefined && tab!==null){
                next((e) => {
                    e.beforeRouteEnter1(Number(tab))
                });
            }else{
                next()
            }
        },
    });
</script>

<script setup lang="ts">

    import {querySrcInfo} from "@pms/api/purchaseRequirements";
    import OrderAPI, {OrderPageVO, OrderPageQuery, OrderFrom,OrderCountVO} from "@/modules/oms/api/order";
    import {IObject} from "@/core/components/CURD/types";
    import {onBeforeRouteUpdate, useRoute, useRouter} from "vue-router";
    import {convertToTimestamp, parseDateTime} from "@/core/utils";
    import moment from 'moment';
    import UploadDialog from "./components/uploadDialog.vue";
    import { ElMessageBox } from "element-plus";
    import Print from "./components/print.vue";
    import { useNavigation } from "@/core/composables";
    import { isEmpty } from "@/core/utils/index.js";

    defineOptions({
        name: "OrderManage",
        inheritAttrs: false,
    })


    const { refreshAndNavigate } = useNavigation();
    const beforeRouteEnter1 = (tab)=>{
        tabs.value.forEach((item)=>{
            if(item.key=== tab){
                item.active=true
            }else{
                item.active=false
            }
        })
        queryParams.orderStatus=tab
        handleQuery();
        localStorage.removeItem('tabs')
    }
    defineExpose({
        beforeRouteEnter1,
    });

    const orderPrintRef = ref()
    const uploadDialog = reactive({
        visible: false,
    });
    const dialogkey = ref(1);
    const submitLoading = ref(false);
    const isClose = ref(false);
    const ifDrag = ref(true);
    const uploadDialogRef = ref();
    const route = useRoute();
    const router = useRouter();
    const { t } = useI18n();
    const queryFormRef = ref(null);
    const dialogVisible = ref(false);
    const loading = ref(false);
    const total = ref(0);
    const maxSyncCount = 20;
    const dialogSyncStatusVisible = ref(false)
    const countTotal = ref<OrderCountVO[]>(
        {
            allCount: 0,
            draftCount: 0,
            approveCount: 0,
            stockUpCount: 0,
            deliveryCount: 0,
            completeCount: 0,
            cancelCount: 0,
        }
    )
    const supplierList = ref([])
    const dateTypeList = ref([
        {
            key: 1,
            value: t('omsOrder.dateTypeList.orderDate')
        },
        {
            key: 2,
            value:t('omsOrder.dateTypeList.planDeliveryDate')
        },
    ])

    const orderTypeOptionList = ref([
        {
            key: 1,
            value: t('omsOrder.orderTypeList[1]')
        },
        {
          key: 2,
          value: t('omsOrder.orderTypeList[2]')
        },
        {
          key: 3,
          value: t('omsOrder.orderTypeList[3]')
        },
        {
          key: 4,
          value: t('omsOrder.orderTypeList[4]')
        },
        {
          key: 5,
          value: t('omsOrder.orderTypeList[5]')
        },
        {
          key: 9,
          value: t('omsOrder.orderTypeList[9]')
        },
    ])
    const syncStatusOptionList = ref([
      {
        key: 1,
        value: t('omsOrder.syncStatusList[1]')
      },
      {
        key: 2,
        value: t('omsOrder.syncStatusList[2]')
      },
    ])

    const currentActiveTab = ref()

    const queryParams = reactive<OrderPageQuery>({
        dateType:1,
        // orderStatus:'',
        page: 1,
        limit: 20,
    });

    const orderList = ref<OrderPageVO[]>();

    const tabs = ref([
        {
            key: '',
            value: t('omsOrder.orderStatusList.all'),
            active: true
        },
        {
            key: 0,
            value: t('omsOrder.orderStatusList.draft'),
            active: false
        },
        {
            key: 1,
            value: t('omsOrder.orderStatusList.check'),
            active: false
        },
        {
            key: 2,
            value: t('omsOrder.orderStatusList.ready'),
            active: false
        },
        {
            key: 3,
            value: t('omsOrder.orderStatusList.send'),
            active: false
        },
        {
            key: 4,
            value: t('omsOrder.orderStatusList.finash'),
            active: false
        },
        {
            key: 5,
            value: t('omsOrder.orderStatusList.close'),
            active: false
        },
    ])

    /** 时间转换 */
    function changeDateRange(val) {
        if(val===1) {
            var date1 = moment().subtract('days', 0).format('YYYY-MM-DD')
            queryParams.dateRange = [date1,date1]
        }else if(val===2){
            var date1 = moment().subtract('days', 1).format('YYYY-MM-DD')
            queryParams.dateRange = [date1,date1]
        }else if(val===3){
            var endDate1 = moment(new Date()).format('YYYY-MM-DD')
            var startDate = moment().subtract('days', 6).format('YYYY-MM-DD')
            queryParams.dateRange = [startDate,endDate1]
        }
    }

    const changePanel = (data) => {
        tabs.value.map(item => item.active = false);
        currentActiveTab.value = data?.key
        data.active =true;
        queryParams.orderStatus = data.key;
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }


    /** 查询 */
    function handleQuery() {
        if (queryParams.orderCode && queryParams.orderCode.length > 50) {
            return ElMessage.error(t("omsOrder.message.orderCodeTips"));
        }
        if (queryParams.customerName && queryParams.customerName.length > 50) {
            return ElMessage.error(t("omsOrder.message.customerNameTips"));
        }
        if (queryParams.productName && queryParams.productName.length > 50) {
            return ElMessage.error(t("omsOrder.message.productNameTips"));
        }
        loading.value = true;
        let params = {
            ...queryParams
        }
        if(queryParams.dateType==1 && queryParams.dateRange && queryParams.dateRange.length>0){
            params.orderCreateTimeStar=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.orderCreateTimeEnd=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
        }
        if(queryParams.dateType==2 && queryParams.dateRange && queryParams.dateRange.length>0){
            params.expectedReceivedTimeStar=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.expectedReceivedTimeEnd=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
        }
        delete params.dateType
        delete params.dateRange
        OrderAPI.getOrderPage(params)
            .then((data) => {
                getOrderCountOfType();
                orderList.value = data.records;
                total.value = parseInt(data.total);
                if(orderList.value && orderList.value.length>0){
                    orderList.value.forEach(item=>{
                        let countryName=item.countryName?item.countryName:''
                        let provinceName=item.provinceName?item.provinceName:''
                        let cityName=item.cityName?item.cityName:''
                        let districtName=item.districtName?item.districtName:''
                        let address=item.address?item.address:''
                        item.deliveryAddress=countryName+provinceName+cityName+districtName+address
                        item.expectedReceivedTimeBase=item.expectedReceivedTimeBase?parseDateTime(item.expectedReceivedTimeBase, "date"):''
                        item.expectedReceivedTimeStar=item.expectedReceivedTimeStar?parseDateTime(item.expectedReceivedTimeStar, "dateTime").split(' ')[1]:''
                        item.expectedReceivedTimeEnd=item.expectedReceivedTimeEnd?parseDateTime(item.expectedReceivedTimeEnd, "dateTime").split(' ')[1]:''
                    })
                }
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 查询 */
    function getOrderCountOfType() {
        OrderAPI.getOrderCountOfType()
            .then((data) => {
                countTotal.value =data
            })
    }

    /** 重置查询 */
    function handleResetQuery() {
        queryFormRef.value.resetFields();
        queryParams.dateType=1;
        queryParams.mine = false;
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }

    function getOrderTypeLabel(val: number) {
        return orderTypeOptionList.value?.find(item => item.key === val)?.value || '-'
    }
    function getSyncStatusLabel(val: number) {
        return syncStatusOptionList.value?.find(item => item.key === val)?.value || '-'
    }

    const multipleSelection = ref([]);
    function handleSelectionChange(val: any,) {
      multipleSelection.value = val;
    }

    const handleSortChange = (data,type) => {
      console.log(data, type,'99999999999999999')
    }

    const unSyncStatusList = ref([]);
    function handleSyncStatus() {
      if(multipleSelection.value?.length > maxSyncCount){
        return ElMessage.warning(t('omsOrder.message.maxSyncCountTips'))
      }
      if(multipleSelection.value?.some(item => !item.canSync)){
        unSyncStatusList.value = multipleSelection.value?.filter(item => !item.canSync).map(item => item.orderCode)
        dialogSyncStatusVisible.value = true;
      }else{
        let params = {
          orderIds: multipleSelection.value?.map(item => item.id)
        }
        OrderAPI.syncHandeStatus(params)
          .then(() => {
            multipleSelection.value = [];
            handleQuery();
            return ElMessage.success(t('omsOrder.message.syncStatusSuccess'))
          }).finally(() => {})
      }
    }

    /*制单人搜索*/
    function submitterUserChange() {
      if(queryParams.submitterUser){
        queryParams.mine = false;
      }
    }
    /*是否 只看我的*/
    function handleMineChange() {
      if(queryParams.mine){
        queryParams.submitterUser = '';
      }
    }


    /** 删除订单 */
    function deleteOrder(row?: IObject) {
        ElMessageBox.confirm(t('omsOrder.message.deleteTips'), t('omsOrder.title.draftDdelete'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                let data = {
                    id:row.id,
                    orderCode:row.orderCode,
                }
                OrderAPI.deleteOrder(data).then(res => {
                    ElMessage.success(t('omsOrder.message.deleteSucess'))
                    handleQuery()
                })
            },
            () => {
                ElMessage.info(t('omsOrder.message.deleteConcel'));
            }
        );
    }

    /*撤回*/
    function recallHandler(id: string) {
      ElMessageBox.confirm(t('omsOrder.message.recallOrderMsg'), t('omsOrder.message.recallOrderTitle'), {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: "warning",
      }).then(
        () => {
          OrderAPI.recallOrder({orderId: id}).then(res => {
            ElMessage.success(t('omsOrder.message.recallOrderSuccess'))
            handleQuery()
          })
        },
        () => {
          ElMessage.info(t('omsOrder.message.recallOrderFail'));
        }
      );
    }

    /*打印订单*/
    async function printOrder(id: string,orderCode: string) {
      const detailData = await  OrderAPI.queryDetailForEdit({ id:id, orderCode:orderCode})
      const printData = {
        title: detailData.customerAttributes == 1 ? detailData.customerName + t('omsOrder.title.printTitle') : detailData.contactPerson + t('omsOrder.title.printTitle') ,//title
        customerName: detailData.customerAttributes == 1 ? detailData.customerName : detailData.contactPerson ,//客户 散客显示收货人
        salesName: detailData.salesName || "-",//销售人员
        orderTypeName: detailData.orderType ? t(`omsOrder.orderTypeList[${detailData.orderType}]`) : '-',//销售类型
        orderCode: detailData.orderCode || '-', //销售单号
        paymentType: detailData.paymentType ? t(`omsOrder.paymentTypeList[${detailData.paymentType}]`) : '-',//结算方式
        approveUserName: detailData.approveUserName || "-",//审核人员
        contactPerson: detailData.contactPerson || "-",//收货人
        submitter: detailData?.submitter || "-",//提交人
        customerMobile: detailData.contactMobile ? detailData.contactAreaCode + " " + detailData.contactMobile :  "-",//电话
        addressFormat: detailData.countryName + detailData.provinceName + detailData.cityName  + detailData.districtName + detailData.address,//收货地址
        orderDetailList:[],//商品明细
        /** 合计金额 */
        totalSaleAmount: null,
        /** 优惠 */
        totalDiscountAmount: '0.00',
        /** 总数量 */
        totalQty: 0,
      }
      printData.orderDetailList = detailData.orderDetailList?.map((list) =>({
        /** 商品名称 */
        productName: list.productName || '-',
        /** 单位 */
        productUnitName: list.productUnitName || '-',
        /** 数量 */
        productQty: list.productQty || '-',
        /** 单价 */
        salePrice: list.saleAmount || '-',
        /** 金额 */
        saleAmount: list.totalSaleAmount || '-',
        /** 备注 */
        remark: list.remark || '-',
      }))

      printData.totalSaleAmount = Number(detailData?.totalDiscountedAmount).toFixed(2);
      printData.totalQty = detailData?.totalProductQty || 0;
      if(!isEmpty(detailData?.totalAmount) && !isEmpty(printData.totalSaleAmount)){
        printData.totalDiscountAmount = Number(Number(detailData?.totalAmount) - Number(printData.totalSaleAmount)).toFixed(2)
      }
      // 执行打印
      nextTick(() => {
        orderPrintRef.value?.handlePrint(printData);
      });
    }

    /** 新增/编辑订单*/
    function addOrder(id?:string,orderCode?:string,type?:string){
        refreshAndNavigate({
            path: "/oms/order/addOrder",
            query: {id:id,orderCode:orderCode,type:type,title:type=='edit'?t("omsOrder.button.editOrder"):t("omsOrder.button.addOrder")}
        });
    }

    /** 查看订单*/
    function detailOrder(id?:string,orderCode?:string,type?:string){
        router.push({
            path: "/oms/order/orderDetail",
            query: {id:id,orderCode:orderCode,type:type,title:type=='check'?t("omsOrder.button.check"):t("omsOrder.button.orderDetail")}
        });
    }

    const handleViewInfo = async (row: any, type: number) => {
        try {
            const data = await OrderAPI.querySrcInfo({ id: row.id });
            row.contactPerson = data.name;
            row.contactMobile = data.mobile;
            row.deliveryAddress = data.address;
            row.isNameDecrypted = true;
            row.isMobileDecrypted = true;
            row.isAddressDecrypted = true;
        } catch (e) {}
    };

    // 导入
    function handleUpload() {
        dialogkey.value++
        uploadDialog.visible = true;
        isClose.value=false
        submitLoading.value=false
    }

    //上传提交
    function onSubmitUpload(data: any) {
        submitLoading.value=true
        let params = {
            file:data.file[0],
        }
        OrderAPI.importOrder(params).then(res => {
            ElMessage.success(t('omsOrder.message.importOrderSucess'))
            submitLoading.value=false
            handleQuery()
        }) .finally(() => {
            isClose.value=true
        });
    }

    onActivated(() => {
        if( localStorage.getItem('tabs')==null || localStorage.getItem('tabs')==undefined){
            handleQuery();
        }
    });

</script>

<style lang="scss" scoped>
    .orderManage{
      height: 100%;
      display: flex;
      flex-direction: column;
      .search-container {
        flex-shrink: 0;
      }
      .table-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
      .el-table {
        flex: 1;
        overflow: auto;
      }
      .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }

        .panelContent {
            display: flex;
            border-bottom: 1px solid #F2F3F4;
            width: 100%;
            margin-bottom: 16px;

            .panelItem {
                font-size: 14px;
                color: #151719;
                padding: 10px 39px;
                cursor: pointer;
                &.active {
                    color: var(--el-color-primary);
                    border-bottom: 2px solid var(--el-color-primary);
                }
            }
        }
        .gred{
            color: #90979E;
        }
        .close-reason{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #90979E;
        }
        .circle-div{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .circle{
                width: 6px;
                height: 6px;
                border-radius: 3px;
                margin-right: 7px;
            }
            .circle-color1{
                background: #29B610;
            }
            .circle-color0{
                background: #D7DBDF;
            }
        }
    }
</style>
<style lang="scss">
</style>


