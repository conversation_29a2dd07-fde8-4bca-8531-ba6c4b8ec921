import type { App } from "vue";
import {hasPerm, hasPermEye} from "./permission";
import { number,focus } from "./number";
import { strategyShow } from "./strategy";

// 全局注册 directive
export function setupDirective(app: App<Element>) {
  // 使 v-hasPerm 在所有组件中都可用
  app.directive("hasPerm", hasPerm);
  app.directive("hasPermEye", hasPermEye);
  app.directive("number", number);
  app.directive("focus", focus);
  // 策略显示控制指令
  app.directive("strategyShow", strategyShow);
}
