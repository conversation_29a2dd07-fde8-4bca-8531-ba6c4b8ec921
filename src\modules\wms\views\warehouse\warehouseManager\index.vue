<template>
  <div class="app-container">
    <div class="warehouseManager-container">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form ref="headFormRef" :model="searchForm" :inline="true">
            <el-form-item
              prop="warehouseCode"
              :label="$t('warehouse.label.warehouseCoding')"
            >
              <el-input
                v-model="searchForm.warehouseCode"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                @keyup.enter="onSearchHandler"
              />
            </el-form-item>
            <el-form-item
              prop="warehouseName"
              :label="$t('warehouse.label.warehouseName')"
            >
              <el-input
                v-model="searchForm.warehouseName"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                @keyup.enter="onSearchHandler"
              />
            </el-form-item>
            <el-form-item prop="status" :label="$t('warehouse.label.status')">
              <el-select
                v-model="searchForm.status"
                :placeholder="$t('common.placeholder.selectTips')"
                clearable
                class="!w-[198px]"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.statusId"
                  :label="item.statusName"
                  :value="item.statusId"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="deptIds" :label="$t('warehouse.label.affiliatedDepartment')">
              <!--<el-cascader
                ref="searchDeptRef"
                v-model="searchForm.deptIds"
                :options="departmentList"
                :props="{ value: 'id', label: 'deptName',  multiple: true, checkStrictly: true }"
                collapse-tags
                clearable
                @change="setDeptIds"
              />-->
              <el-tree-select
                v-model="searchForm.deptIds"
                :data="departmentList"
                :props="deptProps"
                node-key="id"
                multiple
                clearable
                :render-after-expand="false"
                show-checkbox
                check-strictly
                check-on-click-node
                class="!w-[210px]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearchHandler">
                {{ $t("common.search") }}
              </el-button>
              <el-button @click="onResetHandler">
                {{ $t("common.reset") }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="content-card">
        <div class="action-bar">
          <el-button type="primary" @click="onAddHandler">
            {{ $t("warehouse.button.addWarehouse") }}
          </el-button>
        </div>
        <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="tableData"
          highlight-current-row
          border
        >
          <el-table-column
            type="index"
            :label="$t('warehouse.label.sort')"
            width="55"
          />
          <el-table-column
            :label="$t('warehouse.label.warehouseCoding')"
            prop="warehouseCode"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('warehouse.label.warehouseName')"
            prop="warehouseName"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('warehouse.label.country')"
            prop="countryName"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('warehouse.label.province')"
            prop="provinceName"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('warehouse.label.city')"
            prop="cityName"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('warehouse.label.area')"
            prop="districtName"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('warehouse.label.detailAddress')"
            prop="address"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('warehouse.label.contact')"
            prop="contactPerson"
            show-overflow-tooltip
            min-width="160"
          />

          <el-table-column
            :label="$t('warehouse.label.contactNumber')"
            prop="mobile"
            show-overflow-tooltip
            min-width="160"
          >
            <template #default="scope">
              <EncryptPhone :phone="scope.row.mobile" />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('warehouse.label.fixedLine')"
            prop="contactLandline"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('warehouse.label.affiliatedDepartment')"
            prop="deptName"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            :label="$t('warehouse.label.remark')"
            prop="notes"
            show-overflow-tooltip
            min-width="160"
          />
          <el-table-column
            prop="mobile"
            :label="$t('warehouse.label.status')"
            width="120"
            align="center"
          >
            <template #default="scope">
              <el-switch
                :active-text="$t('common.activeBtn')"
                :inactive-text="$t('common.inactiveBtn')"
                inline-prompt
                :model-value="scope.row.status === 1"
                :active-value="true"
                :inactive-value="false"
                @change="(val) => handleStatusChange(val, scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('warehouse.label.operate')"
            fixed="right"
            width="160"
            align="center"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="onEditHandler(scope.row)"
              >
                {{ $t("common.edit") }}
              </el-button>
              <el-button
                type="danger"
                link
                @click="
                  onDeleteHandler(
                    $t('warehouse.message.deleteWarehouse'),
                    'id',
                    scope.row.id
                  )
                "
              >
                {{ $t("common.delete") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="paginationInfo.pageNo"
            v-model:limit="paginationInfo.pageSize"
            @pagination="onPaginationChangeHandler"
          />
        </div>
      </el-card>
    </div>
    <el-drawer
      :key="refresh"
      v-model="showDialog"
      :title="diglogTitle"
      :close-on-click-modal="false"
      width="1800px"
      @close="onCloseHandler"
    >
      <el-form
        :model="contentForm"
        :rules="contentFormRules"
        ref="contentFormRef"
        label-position="top"
      >
        <el-form-item
          :label="$t('warehouse.label.ownWarehouseOrg')"
          prop="roleName"
        >
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="currentTenant.tenantName"
            :maxlength="20"
            clearable
            :disabled="true"
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.warehouseCoding')"
          prop="roleDesc"
        >
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.warehouseCode"
            :maxlength="20"
            clearable
            :disabled="true"
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.warehouseName')"
          prop="warehouseName"
        >
          <el-input
            type="text"
            :placeholder="$t('warehouse.placeholder.input30Tips')"
            v-model="contentForm.warehouseName"
            :maxlength="30"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.warehouseAddress')"
          prop="fullAddress"
        >
          <SelAreaCascader
            v-loading="areaLoading"
            ref="selAreaCascaderRef"
            v-model:defaultCountryInfo="defaultCountryInfo"
            v-model:defaultAreaInfo="defaultAreaInfo"
            v-model:defaultDesAddressInfo="defaultDesAddress"
            v-model:isEditMode="isEditMode"
            @get-country-info="getCountryInfo"
            @get-area-info="getAreaInfo"
            @get-des-address-info="getDesAddressInfo"
          />
        </el-form-item>
        <el-form-item :label="$t('warehouse.label.status')" prop="status">
          <el-switch
            :active-text="$t('common.activeBtn')"
            :inactive-text="$t('common.inactiveBtn')"
            inline-prompt
            v-model="contentForm.status"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.contact')"
          prop="contactPerson"
        >
          <el-input
            type="text"
            :placeholder="$t('warehouse.placeholder.input30Tips')"
            v-model="contentForm.contactPerson"
            :maxlength="30"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.contactNumber')"
          prop="mobile"
        >
          <el-input
            v-model="contentForm.mobile"
            :placeholder="$t('common.placeholder.inputTips')"
            @input.native="mobileInput"
          >
            <template #prepend>
              <el-select
                v-model="contentForm.countryAreaCode"
                style="width: 80px"
              >
                <el-option
                  v-for="item in countryNumCodeList"
                  :key="item.id"
                  :label="item.internationalCode"
                  :value="item.internationalCode"
                />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.fixedLine')"
          prop="contactLandline"
        >
          <el-input
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.contactLandline"
            :maxlength="20"
            @input.native="fixedLineInput"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('warehouse.label.affiliatedDepartment')" prop="">
          <el-cascader
            v-model="contentForm.deptId"
            :options="deptList"
            :props="{ value: 'id', label: 'deptName', checkStrictly: true}"
            clearable
            popper-class="warehouse-manager-dept-cascader"
            ref="deptRef"
            @change="setName"
          />
        </el-form-item>
        <el-form-item :label="$t('warehouse.label.remark')" prop="notes">
          <el-input
            type="textarea"
            show-word-limit
            :placeholder="$t('warehouse.placeholder.input200Tips')"
            v-model="contentForm.notes"
            :maxlength="200"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
          <span class="dialog-footer">
            <el-button @click="onCloseHandler()">
              {{ $t("common.cancel") }}
            </el-button>
            <el-button
              type="primary"
              :loading="dialogLoading"
              @click="saveHandler"
            >
              {{ $t("common.confirm") }}
            </el-button>
          </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import formMixin from "@/modules/wms/mixins/form";
import tableMixin from "@/modules/wms/mixins/table";
import type { FormRules } from "element-plus";
import WarehouseAPI, { warehouseForm } from "@/modules/wms/api/warehouse";
import SelAreaCascader from "@/modules/wms/components/SelAreaCascader.vue";
import CommonAPI from "@/modules/wms/api/common";
import UserAPI from "@/core/api/accountManagement";
import { emitter } from '@/core/utils/eventBus';
import type { CascaderProps } from "element-plus";
import { useUserStore } from "@/core/store";
const userStore = useUserStore();

const departmentList = ref([]);
const deptProps = ref(
  { value: 'id', label: 'deptName', children: 'children' }
)
const searchDeptRef = ref();
const deptList = ref([]);
const deptRef = ref()

function queryDepartmentList() {
  WarehouseAPI.allDeptList().then((data) => {
    departmentList.value = data;
  });
}
function setDeptIds() {
  searchForm.deptIds = []
  // 获取选中的nodeList
  let nodeList = searchDeptRef.value.getCheckedNodes()
  if(nodeList && nodeList.length > 0){
    nodeList.forEach(list=>{
      searchForm.deptIds.push(list.value)
    })
  }
}
function getDepartmentList() {
  WarehouseAPI.allDeptList().then((data) => {
    deptList.value = data;
    if (deptList.value && deptList.value.length > 0) {
      contentForm.deptId = findParentLabel(
        deptList.value,
        contentForm.deptId
      );
    }
  });
}
function findParentLabel(list, str, parents = []) {
  for (let i = 0; i < list.length; i++) {
    const node = list[i];
    if (node.id === str) {
      return [...parents, node.id];
    } else if (node.children && node.children.length > 0) {
      const result = findParentLabel(node.children, str, [...parents, node.id]);
      if (result) {
        return result;
      }
    }
  }
  // 没有找到目标子节点
  return null;
}
function setName() {
  // 获取选中的nodeList
  let nodeList = deptRef.value.getCheckedNodes()
  // 解析node的deptName
  if(nodeList && nodeList.length > 0){
    contentForm.deptId = nodeList[0].value
    contentForm.deptName = nodeList[0].label
  }else{
    contentForm.deptName = null
    contentForm.deptId = null
  }
}

const { proxy } = getCurrentInstance();
const { t } = useI18n();
const defaultCountryInfo = ref("");
const defaultAreaInfo = ref([]);
const defaultDesAddress = ref("");
const selAreaCascaderRef = ref(null);
const refresh = ref(0);
const countryNumCodeList = ref();
const isEditMode = ref(false);

const diglogTitle = computed(() =>
  formType.value === "add"
    ? t("warehouse.title.addWarehouseTitle")
    : t("warehouse.title.editWarehouseTitle")
);
const searchForm = reactive({
  warehouseCode: "",
  warehouseName: "",
  status: "",
  deptIds:[],//部门

});
const currentTenant = ref();
const contentForm = reactive<warehouseForm>({
  id: "",
  warehouseName: "",
  warehouseCode: "系统生成",
  address: "",
  countryId: "",
  countryName: "",
  provinceId: "",
  provinceName: "",
  cityId: "",
  cityName: "",
  districtId: "",
  districtName: "",
  contactPerson: "",
  mobile: "",
  contactLandline: "",
  notes: "",
  status: 1,
  fullAddress: "",
  fullAddressClone: "",
  countryAreaCode: "+86",
  deptId:'',
  deptName:'',
});
const contentFormRules = reactive<FormRules>({
  warehouseName: [
    {
      required: true,
      message: proxy.$t("warehouse.rules.wareHouseName"),
      trigger: "blur",
    },
  ],
  fullAddress: [
    /*  {
      required: true,
      message: proxy.$t("warehouse.rules.address"),
      trigger: ["blur", "change"],
    }, */
    {
      required: true,
      message: proxy.$t("warehouse.rules.address"),
      trigger: ["change", "blur"],
      validator: (rule, value, callback) => {
        if (!defaultCountryInfo.value) {
          callback(new Error(proxy.$t("warehouse.rules.address")));
        } else if (!defaultAreaInfo.value?.length > 0) {
          callback(new Error(proxy.$t("warehouse.rules.address")));
        } else if (!defaultDesAddress.value) {
          callback(new Error(proxy.$t("warehouse.rules.address")));
        } else {
          callback();
        }
      },
    },
  ],
  contactPerson: [
    {
      required: true,
      message: proxy.$t("warehouse.rules.contactPerson"),
      trigger: "blur",
    },
  ],
  mobile: [
    {
      required: true,
      message: proxy.$t("warehouse.rules.mobile"),
      trigger: "blur",
    },
  ],
});
function formatParamsCallbackHandler(params) {
  // params.mobile = params.countryAreaCode + params.mobile;
}
function saveHandler() {
  checkAddress();
  onSaveHandler();
}
function saveCallbackHandler() {
  onSearchHandler();
  emitter.emit("refreshWarehouseList");
}

function addCallbackHandler() {
  isEditMode.value = false;
  contentForm.warehouseName = "";
  contentForm.contactPerson = "";
  contentForm.mobile = "";
  contentForm.contactLandline = "";
  contentForm.notes = "";
  contentForm.warehouseCode = "系统生成";
}

function editCallbackHandler(row) {
  isEditMode.value = true;
  contentForm.mobile = contentForm.mobile.replace(
    contentForm.countryAreaCode,
    ""
  );
  defaultCountryInfo.value = row.countryId;
  if (row.provinceId) {
    defaultAreaInfo.value[0] = row.provinceId;
  }
  if (row.cityId) {
    defaultAreaInfo.value[1] = row.cityId;
  }
  if (row.districtId) {
    defaultAreaInfo.value[2] = row.districtId;
  }

  // defaultAreaInfo.value = Array.of(row.provinceId, row.cityId, row.districtId);
  defaultDesAddress.value = row.address;
  contentForm.fullAddressClone = `${row.countryName}/${row.provinceName}/${row.cityName}/${row.districtName}/${row.address}`;
}

const {
  showDialog,
  dialogLoading,
  formType,
  contentFormRef,
  onAddHandler,
  onEditHandler,
  onSaveHandler,
  onCloseHandler,
} = formMixin({
  contentForm,
  idName: "id",
  uselessParams: ["fullAddress"],
  formAddApi: WarehouseAPI.addWarehouse,
  formEditApi: WarehouseAPI.editWarehouse,
  saveCallback: saveCallbackHandler,
  editCallback: editCallbackHandler,
  closeCallback: closeCallbackHandler,
  formatParamsCallback: formatParamsCallbackHandler,
  addCallback: addCallbackHandler,
});
const {
  loading,
  tableData,
  total,
  paginationInfo,
  headFormRef,
  router,
  path,
  onSearchHandler,
  onResetHandler,
  onPaginationChangeHandler,
  onDeleteHandler,
} = tableMixin({
  searchForm,
  tableGetApi: WarehouseAPI.getWarehousePage,
  tableDeleteApi: WarehouseAPI.deleteWarehouse,
  tableEnableApi: WarehouseAPI.updateStatus,
});

function mobileInput(event) {
  contentForm.mobile = event.replace(/[^\d.]/g, "");
}
function fixedLineInput(event) {
  contentForm.contactLandline = event.replace(/[^\d.]/g, "");
}

function getCountryInfo(data: any) {
  contentForm.countryId = data?.id || "";
  contentForm.countryName = data?.shortName || "";
  defaultCountryInfo.value = data?.id || "";
  checkAddress();
}
function getAreaInfo(data) {
  const pathLabels = data?.pathLabels;
  const pathValues = data?.pathValues;
  pathLabels?.forEach((value, index) => {
    if (index === 0) {
      contentForm.provinceId = pathValues[index];
      contentForm.provinceName = value;
      contentForm.cityId = "";
      contentForm.cityName = "";
      contentForm.districtId = "";
      contentForm.districtName = "";
    } else if (index === 1) {
      contentForm.cityId = pathValues[index];
      contentForm.cityName = value;
      contentForm.districtId = "";
      contentForm.districtName = "";
    } else if (index === 2) {
      contentForm.districtId = pathValues[index];
      contentForm.districtName = value;
    }
  });
  defaultAreaInfo.value = data?.pathValues || [];
  checkAddress();
}
function getDesAddressInfo(data) {
  contentForm.address = data.value;
  defaultDesAddress.value = data.value;
  checkAddress();
}

function checkAddress() {
  if (
    contentForm.countryId.length > 0 &&
    contentForm.provinceId.length > 0 &&
    (contentForm.address.length > 0 || contentForm.fullAddressClone.length > 0)
  ) {
    contentForm.fullAddress = contentForm.countryId;
  } else {
    contentForm.fullAddress = "";
  }
}

function closeCallbackHandler() {
  contentForm.fullAddress = "";
  defaultAreaInfo.value = [];
  defaultCountryInfo.value = "";
  defaultDesAddress.value = "";
  refresh.value++;
}

const statusList = ref([
  {
    statusId: 0,
    statusName: t("common.statusEmun.disable"),
  },
  {
    statusId: 1,
    statusName: t("common.statusEmun.enable"),
  },
]);

const areaLoading = ref(false);
// 获取区号
function getAreaList() {
  areaLoading.value = true;
  UserAPI.getAllCountry()
    .then((data) => {
      countryNumCodeList.value = data;
    })
    .finally(() => {
      areaLoading.value = false;
    });
}
function getCurrentTenant() {
  CommonAPI.getCurrentTenant()
    .then((data) => {
      currentTenant.value = data;
    })
    .finally(() => {});
}
function handleStatusChange(val, row) {
  WarehouseAPI.updateStatus({ ids: [row.id], status: val ? 1 : 0 })
    .then(() => {
      row.status = val ? 1 : 0;
    })
    .finally(() => {});
}
onMounted(() => {
  searchForm.deptIds = []
  if(userStore.user.baseDeptVOList && userStore.user.baseDeptVOList.length > 0){
    userStore.user.baseDeptVOList.forEach(list=>{
      searchForm.deptIds.push(list.id)
    })
  }
  onSearchHandler();
  getCurrentTenant();
  getAreaList();
  queryDepartmentList();
  getDepartmentList();
});
</script>

<style lang="scss" scoped>
  .warehouseManager-container{
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }
    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .action-bar {
        margin-bottom: 12px;
        flex-shrink: 0;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
</style>
<style lang="scss">
  .warehouse-manager-dept-cascader {
    .el-cascader-panel .el-radio__input {
      display: block !important;
    }
    .el-cascader-panel .el-radio {
      display: block !important;
    }
  }
</style>
