<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .expected {
            background-color: #f0f8ff;
            padding: 10px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        .steps {
            background-color: #f9f9f9;
            padding: 10px;
            border-left: 4px solid #28a745;
            margin: 10px 0;
        }
        .note {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>快速入库表单验证测试指南</h1>
    
    <div class="note">
        <strong>注意：</strong>请在开发环境中访问快速入库页面进行测试：
        <a href="http://localhost:3001" target="_blank">http://localhost:3001</a>
    </div>

    <div class="test-case">
        <h3>测试场景1：商品数量填写大于0的值</h3>
        <div class="steps">
            <strong>测试步骤：</strong>
            <ol>
                <li>进入快速入库页面</li>
                <li>在商品列表中，将"入库量"字段填写为大于0的数值（如：10）</li>
                <li>尝试不填写其他必填项（单价、入库金额、库区、产地、入库转换量）</li>
                <li>点击提交按钮</li>
            </ol>
        </div>
        <div class="expected">
            <strong>预期结果：</strong>
            <ul>
                <li>单价字段显示"单价不能为空"错误提示</li>
                <li>入库金额字段显示"入库金额不能为空"错误提示</li>
                <li>库区字段显示"库区不能为空"错误提示</li>
                <li>产地字段显示"产地不能为空"错误提示</li>
                <li>入库转换量字段显示"入库转换量不能为空"错误提示</li>
                <li>表单无法提交</li>
            </ul>
        </div>
    </div>

    <div class="test-case">
        <h3>测试场景2：商品数量不填写（空值）</h3>
        <div class="steps">
            <strong>测试步骤：</strong>
            <ol>
                <li>进入快速入库页面</li>
                <li>在商品列表中，保持"入库量"字段为空</li>
                <li>不填写其他字段（单价、入库金额、库区、产地、入库转换量）</li>
                <li>点击提交按钮</li>
            </ol>
        </div>
        <div class="expected">
            <strong>预期结果：</strong>
            <ul>
                <li>单价、入库金额、库区、产地、入库转换量字段都不显示必填错误提示</li>
                <li>表单可以正常提交</li>
            </ul>
        </div>
    </div>

    <div class="test-case">
        <h3>测试场景3：商品数量填写为0</h3>
        <div class="steps">
            <strong>测试步骤：</strong>
            <ol>
                <li>进入快速入库页面</li>
                <li>在商品列表中，将"入库量"字段填写为0</li>
                <li>不填写其他字段（单价、入库金额、库区、产地、入库转换量）</li>
                <li>点击提交按钮</li>
            </ol>
        </div>
        <div class="expected">
            <strong>预期结果：</strong>
            <ul>
                <li>单价、入库金额、库区、产地、入库转换量字段都不显示必填错误提示</li>
                <li>表单可以正常提交</li>
            </ul>
        </div>
    </div>

    <div class="test-case">
        <h3>测试场景4：动态验证测试</h3>
        <div class="steps">
            <strong>测试步骤：</strong>
            <ol>
                <li>进入快速入库页面</li>
                <li>先将"入库量"填写为10，观察其他字段的验证状态</li>
                <li>然后将"入库量"改为0，观察其他字段的验证状态变化</li>
                <li>再将"入库量"改回10，观察验证状态是否恢复</li>
            </ol>
        </div>
        <div class="expected">
            <strong>预期结果：</strong>
            <ul>
                <li>入库量为10时：其他字段显示必填验证错误</li>
                <li>入库量改为0时：其他字段的必填验证错误消失</li>
                <li>入库量改回10时：其他字段的必填验证错误重新出现</li>
            </ul>
        </div>
    </div>

    <div class="note">
        <strong>技术实现说明：</strong>
        <ul>
            <li>使用自定义验证器 (validator) 替代静态的 required 规则</li>
            <li>在入库量变化时触发相关字段的重新验证</li>
            <li>验证逻辑基于 isQuantityEmpty() 函数判断数量是否为0或空值</li>
        </ul>
    </div>
</body>
</html>
