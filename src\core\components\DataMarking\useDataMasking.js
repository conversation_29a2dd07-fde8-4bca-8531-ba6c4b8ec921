// useDataMasking.js
import { ref, computed } from 'vue'

export function useDataMasking() {
  // 脱敏规则配置
  const maskingRules = {
    // 姓名：保留第一个字符，其他用星号代替
    name: (value) => {
      if (!value || value.length === 0) return ''
      return value.charAt(0) + '*'.repeat(value.length - 1)
    },
    
    // 手机号码：保留前三位和后四位，中间用星号代替
    phone: (value) => {
      if (!value || value.length < 11) return value
      return value.slice(0, 3) + '****' + value.slice(-4)
    },
    
    // 身份证：保留前4位和最后展示部分
    idCard: (value) => {
      if (!value || value.length < 15) return value
      // 根据图片显示，保留前4位，其他用星号代替
      return value.slice(0, 4) + '*'.repeat(value.length - 4)
    },
    
    // 邮箱：根据图片规则，使用星号+隐藏@前面除第一个字符外的所有字符
    email: (value) => {
      if (!value || !value.includes('@')) return value
      const [localPart, domain] = value.split('@')
      if (localPart.length <= 1) return value
      return localPart.charAt(0) + '*'.repeat(localPart.length - 1) + '@' + domain
    }
  }

  // 脱敏处理函数
  const maskData = (value, type) => {
    if (!value || !type) return value
    
    const maskingFunction = maskingRules[type]
    if (!maskingFunction) {
      console.warn(`未找到类型 ${type} 的脱敏规则`)
      return value
    }
    
    return maskingFunction(value)
  }

  // 批量脱敏处理
  const maskBatchData = (dataList, configs) => {
    if (!Array.isArray(dataList)) return dataList
    
    return dataList.map(item => {
      const maskedItem = { ...item }
      
      configs.forEach(config => {
        const { field, type } = config
        if (maskedItem[field]) {
          maskedItem[field] = maskData(maskedItem[field], type)
        }
      })
      
      return maskedItem
    })
  }

  // 判断是否为已脱敏数据
  const isMaskedData = (value) => {
    return value && typeof value === 'string' && value.includes('*')
  }

  // 自定义脱敏规则
  const addMaskingRule = (type, maskingFunction) => {
    if (type && typeof maskingFunction === 'function') {
      maskingRules[type] = maskingFunction
    }
  }

  return {
    maskData,
    maskBatchData,
    isMaskedData,
    addMaskingRule,
    maskingRules
  }
}

// 独立的解密管理器
export function useDataDecryption(decryptApi) {
  const decryptedCache = ref(new Map())
  const loading = ref(false)
  const error = ref(null)

  // 解密单个数据
  const decryptData = async (maskedValue, key) => {
    // 检查缓存
    const cacheKey = key || maskedValue
    if (decryptedCache.value.has(cacheKey)) {
      return decryptedCache.value.get(cacheKey)
    }

    loading.value = true
    error.value = null

    try {
      const result = await decryptApi(maskedValue)
      decryptedCache.value.set(cacheKey, result)
      return result
    } catch (err) {
      error.value = err
      throw err
    } finally {
      loading.value = false
    }
  }

  // 批量解密
  const decryptBatchData = async (maskedDataList) => {
    const promises = maskedDataList.map(data => decryptData(data))
    return Promise.all(promises)
  }

  // 清除缓存
  const clearCache = (key) => {
    if (key) {
      decryptedCache.value.delete(key)
    } else {
      decryptedCache.value.clear()
    }
  }

  return {
    decryptData,
    decryptBatchData,
    clearCache,
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    decryptedCache: computed(() => decryptedCache.value)
  }
}